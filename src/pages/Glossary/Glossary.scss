// Glossary.scss
@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import 'bulma/sass/form/_all';
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

$primary-color: #00c853;
$border-color: #e0e0e0;
$text-color: #000000;

.glossary-tp-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  // padding: 20px 30px;
  font-family: $primary-font !important;
  // max-width: 1500px;
  // margin: 0px auto;
  overflow: visible;
  width: 100%;


  // @media (max-width: 480px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (max-width: 769px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (min-width: 1024px) {
  //   width: 100vw;
  //   max-width: 100%;
  // }

  h1 {
    font-family: $primary-font !important;
    font-size: 2rem !important;
    font-weight: 600 !important;
  }

  h2 {
    color: $primary;
    font-family: $primary-font !important;
    font-size: 1.5rem !important;
    line-height: 3rem !important;
    font-weight: 600;
    margin-bottom: 10px;
  }

  .menu-btns {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    color: black;
    border-radius: 5px;
    padding: 30px 70px;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-top: 20px;
    width: fit-content;
    border: 1px solid black;

    &:hover {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);

      svg #hover {
        fill: #8fbffa !important;
      }
    }

    .menu-btn-text {
      font-size: 16px;
      margin-left: 10px;
    }

    @media (max-width:500px) {
      padding: 25px 21px;
    }
  }

  .horizontal-rule {
    margin-top: 50px;
    width: 95.7%;
    height: 2px;
    border-top: none;
    border-right: none;
    border-bottom: 2px solid rgb(219, 219, 219);
    border-left: none;
    border-image: initial;
  }

  .seo-project-abun-table {
    width: 100%;

    td {
      max-width: 400px;
      font-size: 0.9em;

      &:hover {
        cursor: pointer;
        text-decoration: none !important;
        color: $primary;
      }
    }

    .table-container {

      .abun-table-responsive {

        tbody {
          color: #000;
        }
      }
    }
  }


  .seo-project-header {
    position: relative;
    width: 100%;
    height: 50px;
  }
}

.glossary-card {
  font-family: $primary-font !important;


  // .back-btn {
  //   position: absolute;
  //   left: 14px;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   background: transparent;
  //   border: none;
  //   cursor: pointer;
  //   top: 17px;

    // svg {
    //   @media (max-width: 480px) {
    //     width: 15px;
    //     height: 12px;
    //   }
    // }
  // }

  .gloassy-header {

    h2 {
      font-size: 2rem !important;
      font-weight: 600;
      margin-bottom: 8px;
    }
  
    .glossary-p {
      font-family: $secondary-font !important;
      font-size: 1.125rem !important;
    }
  }

  .glossary-form-container {
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    width: fit-content;

    h3 {
      font-size: 1.4rem;
      font-weight: 600;
      padding: 1rem;
      align-self: start;
    }

    hr {
      background: #e7e7e7;
      height: 1px;
      margin: 0;
      width: 100%;
    }

    .glossary-content {
      justify-items: center;
      display: flex;
      flex-direction: column;
      padding: 1rem;
      padding-left: 1.6rem;
      align-self: flex-start;
    }
    
  }

  .article-title-header:hover {
    color: $primary;
  }

  .loading-row {
    text-align: center;
    vertical-align: middle;
  }

  .loading-text {
    display: inline-block;
    margin: 0;
  }
}

.glossary-topic-card {
  // margin: 40px auto;
  margin-top: 10px;
  // padding: 20px;
  // background-color: #ffffff;
  // border-radius: 10px;
  // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  font-family: $primary-font;
  position: relative;

  // @media(max-width: 769px) {
  //   width: 100%;
  // }

  // @media(min-width: 1024px) {
  //   width: 100vw;
  //   max-width: 100%;
  // }

  .back-btn {
    position: absolute;
    top: 6px;
    left: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    cursor: pointer;

    // svg {
    //   @media (max-width: 480px) {
    //     width: 15px;
    //     height: 12px;
    //   }
    // }
  }

  h1 {
    color: $primary;
    text-align: center;
    font-size: 1.5rem !important;
    font-weight: 500;
    margin-left: 22px;
  }

  h2 {
    color: black;
    text-align: center;
    font-size: 1.5rem !important;
    font-weight: 500;
    display: inline-block;
  }

  .glossary-topic {
    color: $primary;
    text-align: center;
    font-size: 1.5rem !important;
    font-weight: 500;
  }

  .custom_button {
    padding: 10px 20px;
    font-size: 16px;
    color: #fff;
    border: 1px solid #ccc;
    border-radius: 20px;
    background-color: $primary;
    cursor: pointer;
    margin: 0;
    transition: background-color 0.2s ease, color 0.2s ease;
    margin-left: 10px;
  }

  .glossary-content-term {
    font-size: 24px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 16px;
    font-family:  $primary-font !important;
    cursor: pointer;
  }

  .count-glossary {
    font-size: 1rem !important;
    margin-top: 20px;
    margin-bottom: 10px;
    margin-right: 25px;
    // font-weight: bold;
  }

  .create-content-btn {
    margin: auto;
    border-radius: 8px !important;
    margin-right: 25px;
  }

  .table-container {
    // margin-top: 3rem;
    min-height: 20vh;


    .abun-table-no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 30vh;
      font-size: 1.5rem;
      color: #9f9f9f;
    }

    .abun-table-responsive {
      overflow-x: auto;

      h1.abun-table-title {
        font-family:  $primary-font;
        font-size: 1.5rem !important;
        font-weight: 400;
        line-height: 3rem !important;
        text-align: left;
        color: $primary;
      }

      tbody {
        color: #000;


        td:first-child {
          width: 50%;
          text-align: start !important;
        }

        td {
          text-align: center;
        }
      }

      thead {

        // first th
        th:first-child div {
          margin: 0px !important;
        }

        th div {
          // width: 100%;
          margin: auto;
        }
      }

    }


    &.has-checkbox-header {
      .abun-table-responsive {
        overflow-x: auto;

        h1.abun-table-title {
          font-family: $primary-font;
          font-size: 1.5rem !important;
          font-weight: 400;
          line-height: 3rem !important;
          text-align: left;
          color: $primary;
        }

        tbody {

          td:first-child {
            width: 10px;
            /* Minimal width for checkbox */
            text-align: left;
          }

          td:nth-child(2) {
            width: 50%;
            /* Topic column occupies 50% */
            text-align: start !important;
          }

          td {
            text-align: center;
          }
        }

        thead {

          // first th
          th:first-child div {
            margin: 0px !important;
          }

          th:nth-child(2) div {
            margin: 0px !important;
          }

          th div {
            margin: auto;
          }
        }

        @media (max-width: 768px) {
          .btns-container {
            margin-top: 0rem !important;
          }
        }

      }
    }
  }

  /* Table styling */
  .article-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    border-bottom: 2px solid hsl(0, 0%, 86%);
    font-family:  $primary-font;
  }

  .article-table th,
  .article-table td {
    padding: 15px;
  }

  .article-table th:first-child:not([align]) {
    text-align: left;
  }

  .article-table th {
    color: $primary;
    font-weight: bold;
    font-family: $primary-font;
    font-size: 1.3em;
    padding-bottom: 0;
    border-bottom: 2px solid hsl(0, 0%, 86%);
  }

  .article-table td {
    border-bottom: 1px solid hsl(0, 0%, 86%);
    padding-bottom: 8px;
  }

  .article-table td:first-child {
    color: #000000;
    font-size: 1em;
    text-align: left;
    max-width: 400px;
    font-family: $primary-font;

    &:hover {
      cursor: pointer;
      text-decoration: underline;
    }
  }

  .article-table tr:hover {
    background-color: #f9f9f9;
  }

  .delete-btn {
    background: none;
    border: none;
    font-size: 16px;
    color: red;
    cursor: pointer;
  }
}

.glossary-language-article-context {
  width: 100%;
  position: relative;
  top: 126px;
  display: flex;
  align-items: center;
  justify-content: end;

  @media (max-width: 1080px) {
      margin-top: 10px;
      display: contents !important;
  }
  
}

.language-selection{
  @media (max-width: 1080px) {
     width: 50%;
     justify-self: center;
  }
}


.glossary-language-article-context.language-selection{
  display: flex;
  flex-direction: column;
  align-items: center;

  span {
    font-size: 18px;
    color: #333;
    font-weight: 600;
    margin-bottom: 2px;

  }
}

@media (max-width: 1080px) {

  .glossary-language-article-context.language-selection {
    margin-bottom: 6px;
    margin-top: 5px;
  }

  .language-article-context {
    margin-top: 10px;
    display: contents !important;
  }

}

.glossary-table-button{
  min-width: 111px;
}

.glossary-term{
  .abun-table-search-input {
      margin-left: auto;
      margin-right: auto;
      flex: 1;
      max-width: 15rem;
    }
}
