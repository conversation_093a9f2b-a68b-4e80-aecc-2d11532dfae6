import { ColumnDef, create<PERSON><PERSON>um<PERSON><PERSON><PERSON><PERSON>, RowModel } from "@tanstack/react-table";
import React from 'react';
import { useNavigate } from 'react-router-dom';
import AbunButton from "../../../components/AbunButton/AbunButton";
import AbunTable, { IndeterminateCheckbox } from "../../../components/AbunTable/AbunTable";
import CustomContextMenu from '../../../components/CustomContextMenu/CustomContextMenu';
import { KeywordsProjectTableProps, CountryType } from './KeywordProjectTypes';

interface KeywordProjectTableProps {
    tableData: Array<KeywordsProjectTableProps>;
    pageSizes: number[];
    selectedTab: string;
    selectedTabInfo: string;
    setSelectedTab: React.Dispatch<React.SetStateAction<string>>;
    setSelectedTabInfo: React.Dispatch<React.SetStateAction<string>>;
    isFetching: boolean;
    titleGenProgressMap: Record<string, number>;
    titleGenProgressMessageMap: Record<string, string>;
    setTitleGenProgressMap: React.Dispatch<React.SetStateAction<Record<string, number>>>;
    setTitleGenProgressMessageMap: React.Dispatch<React.SetStateAction<Record<string, string>>>;
    total_very_easy_keywords: number;
    total_easy_keywords: number;
    total_moderate_keywords: number;
    total_hard_keywords: number;
    total_very_hard_keywords: number;
    projectId: string;
    icpKeywordProject: boolean,
    selectedRows: KeywordsProjectTableProps[];
    selectedRowsSetter?: (rowModel: RowModel<unknown>) => void;
    handleFetchVolume:  (keywordHashIds: string | string[], selectedLocation: CountryType) => void;
    selectedLocation: CountryType,
    generatingVolume: Record<string, boolean>;
    availableBulkActions: {
        text: string;
        key: string;
    }[];
    bulkActionsEnabled: boolean;
    setShowCreateCustomkeywordModal: (show: boolean) => void;
    handleDelete: (keywordHash: string, keyword: string, difficultyScore: number) => void;
    startTitleGenTask: (keywordHash: string) => void;
    openUrlInNewTab: (url: string) => void;
    failAlertRef: React.MutableRefObject<any>;
    tableRef
}

export function KeywordProjectTable({
    tableRef,
    tableData,
    pageSizes,
    selectedTab,
    selectedTabInfo,
    setSelectedTab,
    setSelectedTabInfo,
    isFetching,
    titleGenProgressMap,
    titleGenProgressMessageMap,
    setTitleGenProgressMap,
    setTitleGenProgressMessageMap,
    total_very_easy_keywords,
    total_easy_keywords,
    total_moderate_keywords,
    total_hard_keywords,
    total_very_hard_keywords,
    projectId,
    selectedRowsSetter,
    icpKeywordProject,
    selectedRows,
    setShowCreateCustomkeywordModal,
    handleFetchVolume,
    selectedLocation,
    generatingVolume,
    availableBulkActions,
    bulkActionsEnabled,
    handleDelete,
    startTitleGenTask,
    openUrlInNewTab,
    failAlertRef
}: KeywordProjectTableProps) {
    const navigate = useNavigate();
    // Create column definitions
    const columnHelper = createColumnHelper<KeywordsProjectTableProps>();
    const columnDefs: ColumnDef<any, any>[] = [
        columnHelper.accessor((row: KeywordsProjectTableProps) => row.keyword, {
            id: 'keyword',
            header: "Keyword",
            cell: keywordProps => {
                return (
                    !keywordProps.row.original.titlesGenerated ? (
                        <span>{keywordProps.row.original.keyword}</span>                    
                ) : (
                    <CustomContextMenu
                        url={`/keyword-project/${projectId}/titles/${keywordProps.row.original.keywordHash}`}
                        CtrlOrMetaClick={() => {
                            openUrlInNewTab(`/keyword-project/${projectId}/titles/${keywordProps.row.original.keywordHash}`);
                        }}
                        normalClick={() => {
                            navigate(`/keyword-project/${projectId}/titles/${keywordProps.row.original.keywordHash}`);
                        }}>
                        <span>{keywordProps.row.original.keyword}</span>
                    </CustomContextMenu>
                )
                )
            },
            enableGlobalFilter: true,
			enableSorting: false,
        }),
        columnHelper.accessor((row: KeywordsProjectTableProps) => row.keywordTraffic,{
            id: 'keywordTraffic',
            header: "Total Traffic Volume",
            cell: props => {
                if (props.row.original.keywordTraffic) {
                    return (
                        <span>{props.row.original.keywordTraffic}</span>
                    );
                } else {
                    return (
                        <button type="button" className={`button is-small more-rounded-borders custom-btn`} style={{ color: 'black' }}>
                            ––
                        </button>
                    );
                }
            },
        }),
        columnHelper.accessor((row: KeywordsProjectTableProps) => row.difficultyScore,{
            id: 'difficultyScore',
            header: "Difficulty",
            cell: props => {
                if (props.row.original.keywordTraffic) {
                    const difficultyScore = props.row.original.keywordTraffic;
                    let difficulty = 'Very Hard';
                    let btn_color = '#8B0000';

                    if (difficultyScore < 1000) {
                        difficulty = 'Very Easy';
                        btn_color = '#6EB173';
                    } else if (difficultyScore >= 1000 && difficultyScore < 15000) {
                        difficulty = 'Easy';
                        btn_color = '#149414';
                    } else if (difficultyScore >= 15000 && difficultyScore < 35000) {
                        difficulty = 'Moderate';
                        btn_color = '#C39C54';
                    } else if (difficultyScore >= 35000 && difficultyScore < 100000) {
                        difficulty = 'Hard';
                        btn_color = '#D22727';
                    }
                    return (
                        <button type="button" className={`button is-small more-rounded-borders custom-btn`} style={{ backgroundColor: btn_color, color: 'white' }}>
                            {difficulty}
                        </button>
                    );
                } else {
                    return (
                        <button type="button" className={`button is-small more-rounded-borders custom-btn`} style={{ color: 'black' }}>
                            ––
                        </button>
                    );
                }
            },
            enableSorting: false,
        }),
        columnHelper.display({
            id: 'action',
            header: () => <span style={{ marginLeft: '-70px' }}>Action</span>,
            cell: cellProps => {
                const keywordHash = cellProps.row.original.keywordHash;
                const isGenerating = titleGenProgressMap[keywordHash];
                const isVolumeGenerating = generatingVolume[keywordHash] === true;

                // Only disable button if already generating titles for 3 keywords and this button is not one of them
                const disableButton = (Object.keys(titleGenProgressMap).length >= 3) && !isGenerating;
                const hasVolume = cellProps.row.original.kwVolume;

                return (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '30px', justifyContent: 'center', width: '100%', height: '100%' }}>
                        {cellProps.row.original.titlesGenerated ? (
                            <CustomContextMenu
                                url={`/keyword-project/${projectId}/titles/${keywordHash}`}
                                CtrlOrMetaClick={() => {
                                    openUrlInNewTab(`/keyword-project/${projectId}/titles/${keywordHash}`);
                                }}
                                normalClick={() => {
                                    navigate(`/keyword-project/${projectId}/titles/${keywordHash}`);
                                }}>
                                <AbunButton type={"primary"}
                                    className={"is-outlined is-small comp-research-table-button"}
                                    disabled={false}
                                    clickHandler={() => { }}>
                                    View Titles
                                </AbunButton>
                            </CustomContextMenu>
                        ) : icpKeywordProject && !hasVolume ? (
                            <AbunButton
                                type="primary"
                                className="is-outlined is-small comp-research-table-button"
                                disabled={isVolumeGenerating}
                                clickHandler={() => {
                                if (!cellProps.row.original.keyword) {
                                    failAlertRef.current?.show("Failed to fetch volume. Keyword is missing.");
                                    setTimeout(() => failAlertRef.current?.close(), 5000);
                                    return;
                                }

                                handleFetchVolume(keywordHash, selectedLocation);
                                }}
                            >
                                 {isVolumeGenerating ? "Searching Volume..." : "Get Search Volume"}
                            </AbunButton>
                            ) : (
                            <AbunButton type={"success"}
                                className={"is-outlined is-small comp-research-table-button"}
                                // Disable button if already generating titles for 3 keywords
                                disabled={disableButton}
                                tooltip={disableButton ?
                                    "You can only generate up to 3 titles simultaneously. Please wait for one or more to finish." :
                                    ""}
                                progress={titleGenProgressMap[keywordHash] || 0}
                                progressColor="#23d160"
                                clickHandler={() => {
                                    if (isGenerating) return;

                                    // Validate keyword
                                    if (!cellProps.row.original.keyword) {
                                        failAlertRef.current?.show("Failed to generate new titles. Keyword is missing.");
                                        setTimeout(() => { failAlertRef.current?.close(); }, 5000);
                                        return;
                                    }

                                    // Immediately update the progress map to show the button as generating
                                    // This will make the button text change immediately
                                    setTitleGenProgressMap(prev => ({ ...prev, [keywordHash]: 1 }));
                                    setTitleGenProgressMessageMap(prev => ({ ...prev, [keywordHash]: "Initializing..." }));

                                    // Start the task
                                    startTitleGenTask(keywordHash);
                                }}>
                                {isGenerating ? "Generating Titles..." : "Generate Title"}
                            </AbunButton>
                            )}                        
                        <svg style={{ textAlign: 'center', marginTop: '0.40rem !important', marginLeft: '25px' }} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
                            onClick={() => handleDelete(cellProps.row.original.keywordHash, cellProps.row.original.keyword, cellProps.row.original.keywordTraffic)}>
                            <g clipPath="url(#clip0_48_5565)">
                                <g clipPath="url(#clip1_48_5565)">
                                    <path d="M3.15356 6.32313C3.44461 10.8562 3.72319 13.2144 3.88856 14.3369C3.97256 14.9046 4.34531 15.3672 4.90346 15.5011C5.66306 15.6839 6.9713 15.8906 9.00075 15.8906C11.0302 15.8906 12.3381 15.6839 13.098 15.5014C13.6559 15.3676 14.0286 14.9049 14.1126 14.3373C14.2783 13.2144 14.5566 10.8562 14.8476 6.32214" stroke="black" strokeWidth="1.05" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M11.3087 3.47962C12.4769 3.50128 13.3871 3.53672 14.0394 3.56986C14.8236 3.60923 15.552 4.02694 15.7712 4.78097C15.804 4.89417 15.8349 5.01394 15.8618 5.14092C15.9911 5.74467 15.5392 6.26344 14.924 6.31561C13.9331 6.39928 12.1195 6.49444 8.99249 6.49444C5.86579 6.49444 4.05191 6.39928 3.0613 6.31561C2.44574 6.26377 1.99129 5.74139 2.15043 5.14486C2.20785 4.92994 2.2784 4.73372 2.35255 4.55948C2.61932 3.93506 3.26146 3.61284 3.93937 3.57544C4.56543 3.54131 5.47663 3.50259 6.69135 3.47962C6.87108 3.07198 7.16548 2.7254 7.53869 2.48211C7.9119 2.23882 8.34781 2.10932 8.79332 2.10938H9.20741C9.65286 2.10938 10.0887 2.23891 10.4618 2.4822C10.835 2.72549 11.129 3.07203 11.3087 3.47962Z" stroke="black" strokeWidth="1.05" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M7.03125 9.32812L7.35937 12.6094" stroke="black" strokeWidth="1.05" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M10.9687 9.32812L10.6406 12.6094" stroke="black" strokeWidth="1.05" strokeLinecap="round" strokeLinejoin="round" />
                                </g>
                            </g>
                            <defs>
                                <clipPath id="clip0_48_5565">
                                    <rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
                                </clipPath>
                                <clipPath id="clip1_48_5565">
                                    <rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
                                </clipPath>
                            </defs>
                        </svg>
                    </div>
                );
            },
        }),
    ];

    if (icpKeywordProject) {
        if (bulkActionsEnabled) {
            columnDefs.splice(0, 0, columnHelper.accessor(
                (row: KeywordsProjectTableProps) => row.keywordHash,
                {
                    id: 'checkbox',
                    header: ({ table }) => {
                        const allSelectableRows = table.getRowModel().rows.filter(
                            (row) => row.original.kwVolume === false
                        );

                        const isAllSelected = allSelectableRows.length > 0 && allSelectableRows.every(row => row.getIsSelected());
                        const isSomeSelected = allSelectableRows.some(row => row.getIsSelected());

                        const toggleSelection = () => {
                            const newSelection: Record<string, boolean> = {};

                            if (!isAllSelected) {
                                for (const row of allSelectableRows) {
                                    newSelection[row.id] = true;
                                }
                            } else {
                                // Deselect only rows with kwVolume === false
                                for (const row of allSelectableRows) {
                                    newSelection[row.id] = false;
                                }
                            }

                            table.setRowSelection(newSelection);
                        };

                        return (
                            <IndeterminateCheckbox
                                checked={isAllSelected}
                                indeterminate={!isAllSelected && isSomeSelected}
                                onChange={toggleSelection}
                                disabled={allSelectableRows.length === 0}
                            />
                        );
                    },
                    cell: ({ row }) => (
                        <IndeterminateCheckbox
                            {...{
                                checked: row.getIsSelected(),
                                disabled: !row.getCanSelect(),
                                indeterminate: row.getIsSomeSelected(),
                                onChange: row.getToggleSelectedHandler(),
                            }}
                            name={"KeywordVolume"}
                            value={row.original.keywordHash}
                        />
                    ),
                    enableGlobalFilter: true,
                    enableSorting: false,
                }
            ));
        } else {
            columnDefs.splice(0, 0);
        }
    }

    return (
        <AbunTable
            ref={tableRef}
            tableContentName={"Keywords"}
            tableData={tableData}
            {...(!icpKeywordProject ? { tableSpace: "fixed" } : {})}
            columnDefs={columnDefs}
            enableSorting={true}
            pageSizes={pageSizes}
            initialPageSize={pageSizes[4]}
            noDataText={"No keywords found"}
            searchboxPlaceholderText={"Search keywords projects..."}
            selectedTab={selectedTab}
            selectedTabInfo={selectedTabInfo}
            rowCheckbox={icpKeywordProject}
            selectedRowsSetter={selectedRowsSetter}
            bulkActions={icpKeywordProject && selectedRows?.length ? availableBulkActions : []}
			bulkActionsEnabled={(icpKeywordProject) ? bulkActionsEnabled : false}
            isListOfTitlesRoute={icpKeywordProject}
            buttons={[
                {
                    text: "Add Custom keyword",
                    type: "primary",
                    isDisabled: false,
                    clickHandler: () => setShowCreateCustomkeywordModal(true),
                    extraClassName: "is-small is-justify-content-space-between",
                },            
            ]}
            handleRowClick={(row, event) => {
                if (!row.titlesGenerated) return;
                const url = `/keyword-project/${projectId}/titles/${row.keywordHash}`;
                if (event?.ctrlKey || event?.metaKey) {                    
                    openUrlInNewTab(url);
                } else {
                    navigate(url);
                }
                }}
            filterTabs={[
                {
                    name: "All Keywords",
                    onClick: () => {
                        setSelectedTab("All Keywords");
                        setSelectedTabInfo("Show all keywords");
                    }
                },
                {
                    name: "Very Easy Keywords",
                    onClick: () => {
                        setSelectedTab("Very Easy Keywords");
                        setSelectedTabInfo("Showing keywords with traffic < 1,000");
                    }
                },
                {
                    name: "Easy Keywords",
                    onClick: () => {
                        setSelectedTab("Easy Keywords");
                        setSelectedTabInfo("Showing keywords with traffic between 1,000 and 15,000");
                    }
                },
                {
                    name: "Moderate Keywords",
                    onClick: () => {
                        setSelectedTab("Moderate Keywords");
                        setSelectedTabInfo("Showing keywords with traffic between 15,000 and 35,000");
                    }
                },
                {
                    name: "Hard Keywords",
                    onClick: () => {
                        setSelectedTab("Hard Keywords");
                        setSelectedTabInfo("Showing keywords with traffic between 35,000 and 100,000");
                    }
                },
                {
                    name: "Very Hard Keywords",
                    onClick: () => {
                        setSelectedTab("Very Hard Keywords");
                        setSelectedTabInfo("Showing keywords with traffic > 100,000");
                    }
                }
            ]}
            applyAction={
                icpKeywordProject
                    ? (action) => {
                        if (action === "Generate Volume") {
                           const keywordHashIds = selectedRows.map(row => row.keywordHash);
                            handleFetchVolume(keywordHashIds, selectedLocation);
                        }
                    }
                    : undefined
                }
        />
    );
}
