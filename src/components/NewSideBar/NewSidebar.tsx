import { useMutation } from "@tanstack/react-query";
import { Dispatch, MutableRefObject, SetStateAction, useEffect, useRef, useState } from "react";
import { Link, NavLink, useLocation, useRouteLoaderData } from "react-router-dom";
import abun_a_logo from "../../assets/images/branding/abun_a_new.webp";
import abunLogo from "../../assets/images/branding/abun_black_text_logo.webp";
import defaultWebIcon from '../../assets/images/icons/defaultCompetitorLogos/default-competitor-logo1.webp';
import { useUIState } from "../../hooks/UIStateContext";
import { BasePageData } from "../../pages/Base/Base";
import { ConnectWebsite } from "../../pages/ConnectWebsite/LazyConnectWebsiteModal";
import { pageURL } from "../../pages/routes";
import {
	featureRequestMutation,
	switchWebsiteMutation,
} from "../../utils/api";
import AbunModal from "../AbunModal/AbunModal";
import GenericButton from "../GenericButton/GenericButton";
import Icon from "../Icon/Icon";
import TextArea from "../TextArea/TextArea";
import './NewSidebar.min.css';

interface SidebarProps {
	activeWebsiteDomain: string | null
	activeWebsiteLogo: string | null
	websiteList: Array<UserWebsite>
	basePath: string | null
	hasLtdPlan: boolean
	showCollapsedSidebar: boolean
	currentPlanName: string
	currentPlanDisplayName: string
	setShowCollapsedSidebar: Dispatch<SetStateAction<boolean>>
	successAlertRef: MutableRefObject<any>
	failAlertRef: MutableRefObject<any>
	emailAddress: string
	userName: string
}

interface featureRequestResponse {
	success: boolean,
	message: string
}

export interface UserWebsite {
	domain: string
	logo_url: string
}

export default function NewSidebar(props: SidebarProps) {
	// --------------------------- STATES ---------------------------	
	const location = useLocation();

	const { hamburgerActive, setHamburgerActive } = useUIState();
	const { showCollapsedSidebar, setShowCollapsedSidebar } = props;
	const [featureRequestMessage, setFeatureRequestMessage] = useState("");
	const [featureRequestErrorMessage, setFeatureRequestErrorMessage] = useState("");
	const [featureRequestSuccessMessage, setFeatureRequestSuccessMessage] = useState("");
	const [featureRequestModalActive, setFeatureRequestModalActive] = useState(false);
	const [websiteDropdownActive, setWebsiteDropdownActive] = useState(false);
	const [profileDropdownActive, setProfileDropdownActive] = useState(false);
	const [showConnectWebsiteModal, setShowConnectWebsiteModal] = useState(false);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [isArticleDropdownOpen, setArticleIsDropdownOpen] = useState(true);
	const [isGuestDropdownOpen, setGuestIsDropdownOpen] = useState(false);
	const [isProgrammaticDropdownOpen, setProgrammaticIsDropdownOpen] = useState(false);
	const [isHelpCenterDropdownOpen, setHelpCenterIsDropdownOpen] = useState(false);
	const [isGlossaryDropdownOpen, setGlossaryIsDropdownOpen] = useState(false);
	const [isRedditDropdownOpen, setRedditIsDropdownOpen] = useState(false);
	const [isAICalculatorDropdownOpen, setAICalculatorIsDropdownOpen] = useState(false);
	const [showTawktoWindow, setShowTawktoWindow] = useState(true)
	const [scrollbarVisible, setScrollbarVisible] = useState(false);
	const sidebarRef = useRef<HTMLDivElement>(null);
	const dropdownRef = useRef<HTMLLIElement>(null);
	const profileDropdownRef = useRef<HTMLDivElement>(null);

	const programmaticDropdownRef = useRef<HTMLLIElement>(null);
	const helpCenterRef = useRef<HTMLLIElement>(null);
	const glossaryDropdownRef = useRef<HTMLLIElement>(null);
	const redditDropdownRef = useRef<HTMLLIElement>(null);
	const guestDropdownRef = useRef<HTMLLIElement>(null);
	const articleDropdownRef = useRef<HTMLLIElement>(null);
	const aiCalculatorDropdownRef = useRef<HTMLLIElement>(null);
	// const navigate = useNavigate();

	const toggleDropdown = () => {
		setIsDropdownOpen(!isDropdownOpen);
	};

	const toggleArticle = () => {
		setArticleIsDropdownOpen(!isArticleDropdownOpen);
		// setRedditIsDropdownOpen(false);
		// setGuestIsDropdownOpen(false);
		// setProgrammaticIsDropdownOpen(false)
		// setGlossaryIsDropdownOpen(false)
		// if (isArticleDropdownOpen) {
		// 	// Just close dropdown, do not navigate
		// 	setArticleIsDropdownOpen(!isArticleDropdownOpen);
		// 	return;
		// }

		// if (location.pathname !== pageURL['createArticle']) {
		// 	navigate(pageURL['createArticle']);
		// }
	};

	const toggleProgrammaticSeo = () => {
		setProgrammaticIsDropdownOpen(!isProgrammaticDropdownOpen);
		// setArticleIsDropdownOpen(false);
		// setRedditIsDropdownOpen(false);
		// setGuestIsDropdownOpen(false);
		// setGlossaryIsDropdownOpen(false);
		// const targetPath = pageURL['programmaticSeo'];
		// if (!isProgrammaticDropdownOpen && location.pathname !== targetPath) {
		// 	navigate(targetPath);
		// }
	};

	const toggleGlossaryGenerator = () => {
		setGlossaryIsDropdownOpen(!isGlossaryDropdownOpen);
		// setArticleIsDropdownOpen(false);
		// setRedditIsDropdownOpen(false);
		// setGuestIsDropdownOpen(false);
		// setProgrammaticIsDropdownOpen(false);
		// const targetPath = pageURL['glossary'];
		// if (!isGlossaryDropdownOpen && location.pathname !== targetPath) {		
		// 	navigate(targetPath);
		// }
	};

	const toggleGuestFinder = () => {
		setGuestIsDropdownOpen(!isGuestDropdownOpen);
		// setArticleIsDropdownOpen(false);
		// setRedditIsDropdownOpen(false);
		// setProgrammaticIsDropdownOpen(false);
		// setGlossaryIsDropdownOpen(false);
		// const targetPath = pageURL['guestPostFinder'];
		// if (!isGuestDropdownOpen && location.pathname !== targetPath) {		
		// 	navigate(targetPath);
		// }
	};

	const toggleRedditFinder = () => {
		setRedditIsDropdownOpen(!isRedditDropdownOpen);
		// setArticleIsDropdownOpen(false);
		// setGuestIsDropdownOpen(false);
		// setProgrammaticIsDropdownOpen(false);
		// setGlossaryIsDropdownOpen(false);
		// const targetPath = pageURL['redditPostFinder'];
		// if (!isRedditDropdownOpen && location.pathname !== targetPath) {
		// 	navigate(targetPath);
		// }
	};

	const toggleAICalculator = () => {
		setAICalculatorIsDropdownOpen(!isAICalculatorDropdownOpen);
	};

	const toggleHelpCenter = () => {
		setHelpCenterIsDropdownOpen(!isHelpCenterDropdownOpen)
	}

	// ----------------------- NON STATE CONSTANTS -----------------------
	const isProduction = process.env.REACT_APP_DRF_DOMAIN === "https://api.abun.com";
	const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;

	// --------------------------- MUTATIONS ---------------------------
	const switchActiveWebsite = useMutation(switchWebsiteMutation);
	const featureRequestMut = useMutation(featureRequestMutation);

	// --------------------------- Refs ---------------------------
	const successAlertRef = useRef<any>(null);
	const failAlertRef = useRef<any>(null);

	// =================================================================
	// --------------------------- MAIN CODE ---------------------------
	// =================================================================
	useEffect(() => {
		const path = location.pathname;

		if (path.startsWith(pageURL['programmaticSeo']) || path.startsWith(pageURL['titleProject']) || path.startsWith(pageURL['listOfTitles'])) {
			setProgrammaticIsDropdownOpen(true);
			setArticleIsDropdownOpen(false)
		} else if (path.startsWith(pageURL['redditPostFinder']), path.startsWith(pageURL['redditPostFinderView']) || path.startsWith(pageURL['redditPostFinderTable'])) {
			setRedditIsDropdownOpen(true);
			setArticleIsDropdownOpen(false)
		} else if (path.startsWith(pageURL['guestPostFinder']) || path.startsWith(pageURL['guestPostFinderView']) || path.startsWith(pageURL['guestPostFinderTable'])) {
			setGuestIsDropdownOpen(true);
			setArticleIsDropdownOpen(false)
		} else if (path.startsWith(pageURL['glossary']) || path.startsWith(pageURL['glossaryTopic']) || path.startsWith(pageURL['glossaryEditor'])) {
			setGlossaryIsDropdownOpen(true);
			setArticleIsDropdownOpen(false)
		} else {
			setArticleIsDropdownOpen(true);
		}
	}, []);


	useEffect(() => {
		if (showConnectWebsiteModal) {
			const handleKeyDown = (event: KeyboardEvent) => {
				if (event.key === "Escape") {
					setShowConnectWebsiteModal(false);
				}
			};

			window.addEventListener("keydown", handleKeyDown);

			return () => {
				window.removeEventListener("keydown", handleKeyDown);
			};
		}
	}, [showConnectWebsiteModal]);

	useEffect(() => {

		if (isDropdownOpen && dropdownRef.current) {
			dropdownRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
		}

		if (!isDropdownOpen) return;

		// other-products dropdown closed while clicking outside
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current && // Ensure dropdownRef is set
				!dropdownRef.current.contains(event.target as Node) // Check if click is outside the dropdown
			) {
				setIsDropdownOpen(false); // Close the dropdown
			}
		};

		// Add event listener to capture outside clicks
		document.addEventListener("click", handleClickOutside);

		// Cleanup the event listener on unmount
		return () => {
			document.removeEventListener("click", handleClickOutside);
		};
	}, [isDropdownOpen]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				profileDropdownRef.current &&
				!profileDropdownRef.current.contains(event.target as Node)
			) {
				setProfileDropdownActive(false);
			}
		};

		if (profileDropdownActive) {
			document.addEventListener("click", handleClickOutside);
		} else {
			document.removeEventListener("click", handleClickOutside);
		}

		return () => {
			document.removeEventListener("click", handleClickOutside);
		};
	}, [profileDropdownActive]);

	useEffect(() => {
		const handleClickOutside = (event) => {

			// Only close the dropdowns while clicking oustside when the sidebar is collapsed
			if (showCollapsedSidebar) {

				// Articles
				if (
					articleDropdownRef.current &&
					!articleDropdownRef.current.contains(event.target)
				) {
					setArticleIsDropdownOpen(false);
				}

				// Programmatic SEO
				if (
					programmaticDropdownRef.current &&
					!programmaticDropdownRef.current.contains(event.target)
				) {
					setProgrammaticIsDropdownOpen(false);
				}

				// Glossary Generator
				if (
					glossaryDropdownRef.current &&
					!glossaryDropdownRef.current.contains(event.target)
				) {
					setGlossaryIsDropdownOpen(false);
				}

				// Guest Post Finder
				if (
					guestDropdownRef.current &&
					!guestDropdownRef.current.contains(event.target)
				) {
					setGuestIsDropdownOpen(false);
				}

				// Reddit Post  Finder
				if (
					redditDropdownRef.current &&
					!redditDropdownRef.current.contains(event.target)
				) {
					setRedditIsDropdownOpen(false);
				}

				// Help Center
				if (
					helpCenterRef.current &&
					!helpCenterRef.current.contains(event.target)
				) {
					setHelpCenterIsDropdownOpen(false);
				}

				// AI Calculator Generator
				if (
					aiCalculatorDropdownRef.current &&
					!aiCalculatorDropdownRef.current.contains(event.target)
				) {
					setAICalculatorIsDropdownOpen(false);
				}

			}
		};

		document.addEventListener('mousedown', handleClickOutside);

		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [showCollapsedSidebar]);

	// Check weather scollbar is visible or not
	useEffect(() => {
		const el = sidebarRef.current;
		if (!el) return;

		const checkScrollbar = () => {
			const visible = el.scrollHeight > el.clientHeight;
			setScrollbarVisible(visible);
		};

		const observer = new ResizeObserver(checkScrollbar);
		observer.observe(el);

		checkScrollbar();

		return () => observer.disconnect();
	}, []);

	function handleWebsiteChange(newWebsiteDomain: string) {
		switchActiveWebsite.mutate(newWebsiteDomain, {
			onSuccess: () => {
				window.location.href = pageURL['createArticle'];
			},
			onError: (error) => {
				console.error(error);
			}
		});
	}

	function checkForUnsavedChanges(event: any) {
		let unsavedSettings: boolean = JSON.parse(localStorage.getItem("unsavedSettings") || "false");
		if (unsavedSettings) {
			if (window.confirm("You have unsaved changes. Are you sure you want to leave this page?")) {
				localStorage.setItem("unsavedSettings", "false");
			} else {
				// prevent navigation
				event.preventDefault();
				return false;
			}
		} else {
			localStorage.removeItem("unsavedSettings");
		}
	}

	function featureRequestModalContent() {
		return (
			<>
				<div className={"integration-modal-logo"}>
					<img src={abunLogo} alt="abun_logo" width={"auto"} height={64} />
				</div>
				<div className={"block"}>
					<p>
						We're always looking to improve and your feedback is invaluable to us.
					</p>
				</div>
				<div className={"block"}>
					<p>
						If you have a suggestion for a new feature or enhancement, please let us know! Describe the feature you would like to see in below message box.
					</p>
				</div>
				<div className={"block"}>
					<p className={"font-secondary"}>
						We appreciate your input and look forward to hearing your ideas!
					</p>
				</div>
				<div className={"mt-5"}>
					<label className={"label"}>
						Describe the feature you want
						<TextArea value={featureRequestMessage}
							placeholder={"Please Describe Your Idea."}
							onChange={(val) => {
								setFeatureRequestMessage(val);
							}} />
					</label>
				</div>
				<GenericButton text={featureRequestMut.isLoading ? "Sending..." : "Send"}
					type={"success"}
					disable={featureRequestMut.isLoading}
					additionalClassList={["mt-6", "pl-6", "pr-6"]}
					clickHandler={() => {
						setFeatureRequestErrorMessage("");
						setFeatureRequestSuccessMessage("");

						const featureRequestMessageLength = featureRequestMessage.length;
						if (featureRequestMessageLength < 20 || featureRequestMessageLength > 500) {
							setFeatureRequestErrorMessage("Please enter a message between 20-500 characters.");
						} else {
							featureRequestMut.mutate(featureRequestMessage, {
								onSuccess: (data) => {
									let response: featureRequestResponse = data['data'];
									if (response.success) {
										setFeatureRequestSuccessMessage(response.message);
									} else {
										setFeatureRequestErrorMessage(response.message);
									}
								},
								onError: () => {
									setFeatureRequestErrorMessage("Oops! Something went wrong. Please try again in some time.");
								}
							});
						}
					}} />
				<div className="mt-4">
					<p className={"has-text-danger"}>{featureRequestErrorMessage}</p>
					<p className={"has-text-success"}>{featureRequestSuccessMessage}</p>
				</div>
			</>
		)
	}

	useEffect(() => {
		const pagesWithoutSidebar = ["/checkout/success", "/articles/edit/", "/integration/wp/success", "/integration/webflow/success", "/integration/google/success", "/auth/login", "/auth/signup", "/auth/forgot-password", "/auth/reset-password", "/auth/verify-email", "/auth/verify-email/success", "/auth/verify-email/resend", "/signup-plan-selection/"];
		const currentPath = location.pathname;

		const shouldHideSidebar = pagesWithoutSidebar.some(path => currentPath.startsWith(path));

		if (!shouldHideSidebar) {
			window.Tawk_API?.hideWidget();
		}
		else {
			window.Tawk_API?.showWidget();
		}
	}, [location.pathname]);

	const showChatWindow = () => {
		if (window.Tawk_API) {
			if (showTawktoWindow) {
				window.Tawk_API.showWidget();
				window.Tawk_API.toggle()
				setShowTawktoWindow(false)
			}
			else {
				window.Tawk_API.toggle()
				window.Tawk_API.hideWidget();
				setShowTawktoWindow(true)
			}
		}
	};

	return (
		<>
			<aside className={`menu new-sidebar ${hamburgerActive ? "full-height" : ""} ${showCollapsedSidebar ? "collapsed" : ""}`}>
				{/* ============================================================================================= */}
				{/* ----------------------------------- Feature Request Modal ----------------------------------- */}
				{/* ============================================================================================= */}
				<AbunModal active={featureRequestModalActive}
					headerText={"Abun Feature Request"}
					closeable={true}
					closeableKey={true}
					hideModal={() => {
						setFeatureRequestModalActive(false);
						setFeatureRequestErrorMessage("");
						setFeatureRequestSuccessMessage("");
					}}>
					{featureRequestModalContent()}
				</AbunModal>

				<div className={"mobile-extra"}>
					<img src={abunLogo} alt="Abun Logo" className={"mobile-logo"} />
					<span className={"hamburger"} onClick={() => {
						setHamburgerActive(!hamburgerActive);
					}}>
						<Icon iconName={"hamburger"} height={"1.65em"} width={"1.65em"} />
					</span>
				</div>
				<section>
					<div className="sidebar-header">
						{/* ------------------------- LOGO ------------------------- */}
						<div className="logo-container">
							<img src={abunLogo} className={`abun-logo ${showCollapsedSidebar ? "is-hidden" : ""}`} alt="Abun Logo" width="128" height="auto" />
							<img src={abun_a_logo} className={`abun-logo ${!showCollapsedSidebar ? "is-hidden" : ""}`} style={{ display: !showCollapsedSidebar ? "none" : "block" }} alt="Abun Logo" width="64" height="auto" />
							<div className="triangle-btn is-clickable" onClick={() => setShowCollapsedSidebar((prev) => !prev)} />
						</div>
					</div>

					{/* ------------------------- WEBSITE SELECTION DROPDOWN ------------------------- */}
					<ul className={"menu-list mt-4"}>
						<div className={`dropdown w-100 ${websiteDropdownActive && "is-active"}`}
							onClick={() => {
								setWebsiteDropdownActive(!websiteDropdownActive)
								// on clicking outside the dropdown, close the dropdown
								document.addEventListener("click", (e) => {
									if (!(e.target as HTMLElement).closest(".dropdown")) {
										setWebsiteDropdownActive(false);
									}
								});
							}
							}>
							<div className="dropdown-trigger w-100">
								<button className="button w-100 is-rounded" aria-haspopup="true" aria-controls="dropdown-menu">
									{props.activeWebsiteDomain ?
										<span className="is-flex is-align-items-center">
											<img src={props.activeWebsiteLogo as string}
												alt={"website logo"}
												onClick={checkForUnsavedChanges} className={"sidebar-website-list-logo"}
												width={20}
												height={"auto"}
												onError={({ currentTarget }) => {
													currentTarget.onerror = null
													currentTarget.src = defaultWebIcon;
												}} />
											<span className="link-text ml-4">
												{props.activeWebsiteDomain.startsWith('default') && props.activeWebsiteDomain.endsWith('.xyz') ? 'Add Project' : props.activeWebsiteDomain.length > 17
													? props.activeWebsiteDomain.slice(0, 17) + '...' : props.activeWebsiteDomain}
												{/* slice it if website domain name is more than 17 character */}
											</span>
										</span> :
										<span><Icon iconName={"plus-circle"} additionalClasses={["icon-primary"]} /><span className="link-text ml-2">Add Project</span></span>}
									<svg xmlns="http://www.w3.org/2000/svg" style={{ marginRight: scrollbarVisible ? '1.45rem' : '0.6rem' }} width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
										<path d="m7 15 5 5 5-5"></path><path d="m7 9 5-5 5 5"></path>
									</svg>
								</button>
							</div>
							<div className="dropdown-menu" id="dropdown-menu" role="menu">
								<div className="dropdown-content">
									<div className={"dropdown-item sidebar-connect-website-list-item"}>
										<div
											className="sidebar-add-website"
											onClick={() => {
												setShowConnectWebsiteModal(true);
											}}>
											{/* <Icon iconName={"plus-circle"} /> */}
											<svg xmlns="http://www.w3.org/2000/svg" width="27" height="26" viewBox="0 0 24 24" fill="none" stroke="#52516e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
												<path d="M5 12h14"></path>
												<path d="M12 5v14"></path>
											</svg>
											<span className="ml-3">Add Project</span>
										</div>
									</div>
									{props.websiteList.map(website => (
										<div key={website.domain}>
											<div className="dropdown-item sidebar-user-website-list-item"
												onClick={() => {
													if (props.activeWebsiteDomain !== website.domain) {
														handleWebsiteChange(website.domain);
													}
												}}>
												<img src={website.logo_url}
													alt={"website logo"}
													onClick={checkForUnsavedChanges} className={"sidebar-website-list-logo"}
													width={20}
													height={"auto"}
													onError={({ currentTarget }) => {
														currentTarget.onerror = null
														currentTarget.src = defaultWebIcon;
													}} />
												<span className={`ml-3 ${props.activeWebsiteDomain === website.domain ? "has-text-primary" : ""}`}>
													{website.domain.startsWith("default-") && website.domain.endsWith(".xyz") ? "Add Project" : website.domain.length > 19 ? website.domain.slice(0, 19) + '...' : website.domain}
												</span>
											</div>
											{/* <hr className="dropdown-divider" /> */}
										</div>
									))}
								</div>
							</div>
						</div>
					</ul>

					{/* ------------------------- SIDEBAR ITEMS ------------------------- */}
					<div className="sidebar-items" ref={sidebarRef}>

						<div className="is-flex is-flex-direction-column">
							<div className="mt-4 is-size-7" style={{ marginLeft: '1.1rem' }}>
								Tools
							</div>
							<ul className="menu-list">
								{/* ------------------------- All ARTICLES ------------------------- */}
								<li className="mt-2 articles subcategory-menu">
									<NavLink to={pageURL['createArticle']} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>

										<svg xmlns="http://www.w3.org/2000/svg" className="svg-article-icon" fill="none" viewBox="0 0 48 48" height="25" width="25">
											<g>
												<path stroke="#000000" d="M7 36.3529c0.3697 6.1654 -2.5 7.4118 -4 7.4118C3 43.7647 9.5 45 19 45c4.4849 0 8.3011 -0.2753 11.0805 -0.566 2.8661 -0.2997 4.9294 -2.7553 4.8495 -5.6359C34.6912 30.1945 33.9035 22.81 33.5424 13c-0.1126 -3.06039 0 -9 4.4576 -10L12 3C8.68629 3 6.00548 5.68197 6.05053 8.99537 6.13668 15.3307 6.3697 25.8418 7 36.3529Z" strokeWidth="3" />
												<path stroke="#000000" d="M37.9998 3c-4.4569 0.99986 -4.5702 6.93788 -4.4576 9.9987 4.3993 -0.0219 7.398 -0.3069 9.2207 -0.5631 1.3335 -0.1874 2.2369 -1.3427 2.2369 -2.68934v-0.6487c0 -3.90244 -2.9999 -6.09756 -6 -6.09756l-1 0Z" strokeWidth="3" />
												<path stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M7 36.3529c0.3697 6.1654 -2.5 7.4118 -4 7.4118C3 43.7647 9.5 45 19 45c4.4849 0 8.3011 -0.2753 11.0805 -0.566 2.8661 -0.2997 4.9294 -2.7553 4.8495 -5.6359C34.6912 30.1945 33.9035 22.81 33.5424 13c-0.1126 -3.06039 0 -9 4.4576 -10L12 3C8.68629 3 6.00548 5.68197 6.05053 8.99537 6.13668 15.3307 6.3697 25.8418 7 36.3529Z" strokeWidth="3" />
												<path stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m37.9996 3.00001 1 -0.00001c3.0001 0 6 2.19512 6 6.09756v0.6487c0 1.34664 -0.9034 2.50194 -2.2369 2.68934 -1.8227 0.2562 -4.8214 0.5412 -9.2207 0.5631" strokeWidth="3" />
												<path stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M17 13h10" strokeWidth="3" />
												<path stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m13 20 14 0" strokeWidth="3" />
												<path stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m13.5 27 14 0" strokeWidth="3" />
												<path stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m14 34 14 0" strokeWidth="3" />
											</g>
										</svg>
										<span className="link-text ml-4">AI Articles</span>
									</NavLink>

									{isArticleDropdownOpen ?
										<ul className="dropdown-menu-container">
											{/* ------------------------- CREATE ARTICLE ------------------------- */}
											{/* <li className="articles subcategory-menu">
												<NavLink to={pageURL['createArticle']} className={({ isActive }) => isActive ? "is-active" : ""}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">Create Article</span>
												</NavLink>
											</li> */}

											{/* ------------------------- SHOW ARTICLES ------------------------- */}
											{/* <li className="articles subcategory-menu">
												<NavLink to={pageURL['showArticles']}
													className={({ isActive }) => isActive ? "is-active" :
														`${document.getElementById("howToArticles")?.classList.contains("is-active") ? "is-active" : `${document.getElementById("listicles")?.classList.contains("is-active") ? "is-active" : ""}`}`}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">Generated Articles</span>
												</NavLink>
											</li> */}

											{/* ------------------------- GSC KEYWORDS ------------------------- */}
											{/* <li className={"articles subcategory-menu"}>
												<NavLink to='/keyword-research?page=gsc'
													className={({ isActive }) =>
														isActive && location.search.includes("page=gsc") ? 'is-active' : ''
													}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">GSC Keyword to Article</span>
												</NavLink>
											</li> */}

											{/* ------------------------- BULK KEYWORDS ------------------------- */}
											{/* <li className={"articles subcategory-menu"}>
												<NavLink to='/keyword-research?page=csv'
													className={({ isActive }) =>
														isActive && location.search.includes("page=csv") ? 'is-active' : ''
													}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">Bulk Keyword to Article</span>
												</NavLink>
											</li> */}

											{/* {!isProduction && (<li className="articles subcategory-menu">
												<NavLink to='/optimize-publish-article-steps'
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text" style={{ whiteSpace: "nowrap" }}>Optimize Published Article</span>
												</NavLink>
											</li>)} */}

											{/* ------------------------- Website Scanning ------------------------- */}
											{/* <li className={"articles subcategory-menu"}>
												<NavLink to={pageURL['websiteScanning']} className={({ isActive }) => isActive ? "is-active" : ""}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">Internal Link Manager</span>
												</NavLink>
											</li> */}

											{/* ------------------------- ARTICLE KEYWORDS ------------------------- */}
											{/* <li className="articles subcategory-menu">
											<NavLink to={pageURL['keywordResearch']} className={({ isActive }) => isActive ? "is-active" : ""}
												onClick={(e) => {
													checkForUnsavedChanges(e);
													setHamburgerActive(false);
												}}>
												<span className="articles-li-text">Keyword Projects</span>
											</NavLink>
										</li> */}

											{/* ------------------------- KEYWORD to ARTICLE ------------------------- */}
											{/* <li className="articles subcategory-menu">
											<NavLink to='/keyword-research?page=ai-keyword'
												onClick={(e) => {
													checkForUnsavedChanges(e);
													setHamburgerActive(false);
												}}>
												<span className="articles-li-text">AI Keyword to Article</span>
											</NavLink>
										</li> */}

											{/* ------------------------- STEAL KEYWORDS ------------------------- */}
											{/* <li className={"articles subcategory-menu"}>
										    <NavLink to={pageURL['competitorResearch']} className={({ isActive }) => isActive ? "is-active" : ""}
										    	onClick={(e) => {
										    	checkForUnsavedChanges(e);
										    	setHamburgerActive(false);
										    	}}>
										    	<span className="articles-li-text">Steal Competitor Keyword</span>
										    </NavLink>
										</li> */}

											{/* ------------------------- LONGTAIL KEYWORDS ------------------------- */}
											{/* <li className={"articles subcategory-menu"}>
										    <NavLink to='/keyword-research?page=longtail'
										    	onClick={(e) => {
										    	checkForUnsavedChanges(e);
										    	setHamburgerActive(false);
										    	}}>
										    	<span className="articles-li-text">Longtail Keyword to Article</span>
										    </NavLink>
										</li> */}

											{/* ------------------------- SETTINGS ------------------------- */}
											{/* <li className="subcategory-menu articles">
												<NavLink to={props.websiteList.length === 0 ? pageURL['connectWebsite'] : pageURL['settings']} className={({ isActive }) => (isActive && !window.location.href.endsWith("?tab=integration")) ? "is-active settings" : "settings"}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
														let interval = setInterval(() => {
															const imagesTab = document.getElementById("images-tab");
															if (imagesTab) {
																imagesTab.click();
																clearInterval(interval);
															}
														}, 500);
													}}>
													<span className="articles-li-text">Article Settings</span>
												</NavLink>
											</li> */}
										</ul>
										: ""}
								</li>

								{/* ------------------------- Keyword Research Projects  ------------------------- */}
								<li className="mt-2 articles subcategory-menu">
									<NavLink to={pageURL['keywordResearch']}
										className={({ isActive }) => isActive && !location.search ? 'is-active' : ''}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-10 -10 320 320" id="Search-Visual--Streamline-Plump" height="320" width="320">
											<g id="search-visual">
												<path id="Vector" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M81.25 18.75s-19.287499999999998 0 -39.725 1.20625a22.875 22.875 0 0 0 -21.5625 21.5625C18.75 61.956250000000004 18.75 81.25 18.75 81.25M218.75 18.75s19.287499999999998 0 39.725 1.20625a22.875 22.875 0 0 1 21.5625 21.5625C281.25 61.956250000000004 281.25 81.25 281.25 81.25M218.75 281.25s19.287499999999998 0 39.725 -1.20625a22.875 22.875 0 0 0 21.5625 -21.5625C281.25 238.04375000000002 281.25 218.75 281.25 218.75M81.25 281.25s-19.287499999999998 0 -39.725 -1.20625a22.875 22.875 0 0 1 -21.5625 -21.5625C18.75 238.04375000000002 18.75 218.75 18.75 218.75" stroke-width="20"></path>
												<path id="Union" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M207.3125 179.09375A80.86875 80.86875 0 0 0 218.75 137.5c0 -44.875 -36.375 -81.25 -81.25 -81.25S56.25 92.625 56.25 137.5s36.375 81.25 81.25 81.25a80.875 80.875 0 0 0 42.10625 -11.75l0.34375 0.41250000000000003c6.875000000000001 8.3 13.65 16.46875 21.23125 24.51875 6.7875000000000005 7.2124999999999995 17.025000000000002 8.13125 24.224999999999998 1.33125a207.78750000000002 207.78750000000002 0 0 0 8.28125 -8.28125c6.800000000000001 -7.1937500000000005 5.875 -17.4375 -1.33125 -24.224999999999998 -8.05625 -7.581250000000001 -16.225 -14.35 -24.51875 -21.224999999999998l-0.525 -0.43750000000000006Z" stroke-width="20"></path>
												<path id="Ellipse 17" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M100 137.5a37.5 37.5 0 1 0 75 0 37.5 37.5 0 1 0 -75 0" stroke-width="20"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Keyword Research Projects</span>
									</NavLink>
								</li>

								{/* ------------------------- AI Keyword Research  ------------------------- */}
								<li className="mt-2 subcategory-menu">
									<NavLink to='/keyword-research?page=ai-keyword'
										className={({ isActive }) =>
											isActive && location.search.includes("page=ai-keyword") ? 'is-active' : ''
										}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Telescope--Streamline-Plump" height="48" width="48">
											<g id="telescope--science-experiment-star-gazing-sky-night-space-universe-astronomy-astronomy">
												<path id="Subtract" fill="" d="M18.8513 14.4541c-3.6988 1.5274 -7.6607 3.1364 -10.46458 4.2027l2.16408 8.0763c2.9613 -0.4786 7.197 -1.0661 11.1639 -1.5927 -0.4003 -1.1602 -0.9204 -2.8491 -1.5781 -5.3039 -0.6578 -2.4549 -1.0518 -4.1775 -1.2853 -5.3824Z" stroke-width="3"></path>
												<path id="Rectangle 1098" fill="" d="M18.7225 13.7515c-0.2233 -1.3088 0.454 -2.3894 1.6309 -3.004 4.1877 -2.187 9.8988 -4.68791 16.3022 -6.65979 1.8722 -0.57652 3.8279 0.38298 4.4641 2.23572 0.5832 1.69867 1.3432 4.10867 2.1987 7.30137 0.8555 3.1927 1.4023 5.6599 1.7465 7.4226 0.3755 1.9226 -0.8385 3.7314 -2.7481 4.1682 -6.5315 1.494 -12.7279 2.1837 -17.448 2.3836 -1.3265 0.0562 -2.4534 -0.541 -2.9144 -1.7861 -0.4247 -1.1471 -1.0201 -2.998 -1.8182 -5.9766s-1.2079 -4.8792 -1.4137 -6.085Z" stroke-width="3"></path>
												<path id="Rectangle 1099" fill="" d="M8.1496 17.8654c-0.3048 -0.9982 -1.16581 -1.6661 -2.18839 -1.4569 -0.20537 0.0421 -0.43063 0.0948 -0.67628 0.1606 -0.24565 0.0658 -0.46708 0.1328 -0.66595 0.1991 -0.99021 0.3301 -1.40192 1.339 -1.16674 2.3559 0.2332 1.0084 0.61465 2.5585 1.22961 4.8535 0.61496 2.2951 1.05964 3.8282 1.36188 4.8181 0.3048 0.9983 1.16581 1.6662 2.18838 1.4569 0.20537 -0.042 0.43063 -0.0947 0.67629 -0.1606 0.24565 -0.0658 0.46708 -0.1328 0.66594 -0.1991 0.99026 -0.33 1.40196 -1.3389 1.16676 -2.3559 -0.2332 -1.0084 -0.6147 -2.5584 -1.22962 -4.8535 -0.61496 -2.295 -1.05964 -3.8281 -1.36188 -4.8181Z" stroke-width="3"></path>
												<path id="Rectangle 1097" stroke="#000000" stroke-linejoin="round" d="M18.7225 13.7515c-0.2233 -1.3088 0.454 -2.3894 1.6309 -3.004 4.1877 -2.187 9.8988 -4.68791 16.3022 -6.65979 1.8722 -0.57652 3.8279 0.38298 4.4641 2.23572 0.5832 1.69867 1.3432 4.10867 2.1987 7.30137 0.8555 3.1927 1.4023 5.6599 1.7465 7.4226 0.3755 1.9226 -0.8385 3.7314 -2.7481 4.1682 -6.5315 1.494 -12.7279 2.1837 -17.448 2.3836 -1.3265 0.0562 -2.4534 -0.541 -2.9144 -1.7861 -0.4247 -1.1471 -1.0201 -2.998 -1.8182 -5.9766s-1.2079 -4.8792 -1.4137 -6.085Z" stroke-width="3"></path>
												<path id="Subtract_2" stroke="#000000" stroke-linejoin="round" d="M18.8513 14.4541c-3.6988 1.5274 -7.6607 3.1364 -10.46458 4.2027" stroke-width="3"></path>
												<path id="Subtract_3" stroke="#000000" stroke-linejoin="round" d="M10.5508 26.7333c2.9613 -0.4785 7.197 -1.066 11.1639 -1.5927" stroke-width="3"></path>
												<path id="Rectangle 135" stroke="#000000" stroke-linejoin="round" d="M8.1496 17.8654c-0.3048 -0.9982 -1.16581 -1.6661 -2.18839 -1.4569 -0.20537 0.0421 -0.43063 0.0948 -0.67628 0.1606 -0.24565 0.0658 -0.46708 0.1328 -0.66595 0.1991 -0.99021 0.3301 -1.40192 1.339 -1.16674 2.3559 0.2332 1.0084 0.61465 2.5585 1.22961 4.8535 0.61496 2.2951 1.05964 3.8282 1.36188 4.8181 0.3048 0.9983 1.16581 1.6662 2.18838 1.4569 0.20537 -0.042 0.43063 -0.0947 0.67629 -0.1606 0.24565 -0.0658 0.46708 -0.1328 0.66594 -0.1991 0.99026 -0.33 1.40196 -1.3389 1.16676 -2.3559 -0.2332 -1.0084 -0.6147 -2.5584 -1.22962 -4.8535 -0.61496 -2.295 -1.05964 -3.8281 -1.36188 -4.8181Z" stroke-width="3"></path>
												<path id="Vector 1661" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m21 45 8 -17 8 17" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">AI Keyword Research</span>
									</NavLink>
								</li>

								{/* ------------------------- Manual Keyword Research  ------------------------- */}
								<li className="mt-2 subcategory-menu">
									<NavLink to='/keyword-research?page=manual'
										className={({ isActive }) =>
											isActive && location.search.includes("page=manual") ? 'is-active' : ''
										}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" id="Input-Box--Streamline-Plump" height="24" width="24">
											<g>
												<path id="Rectangle 1097" fill="" d="M21.343999999999998 8.156374999999999c-0.1135625 -0.750375 -0.7484583333333333 -1.2554166666666668 -1.5065000000000002 -1.2913541666666666C18.409583333333334 6.7979375 15.709958333333335 6.708333333333334 11.5 6.708333333333334c-4.209958333333334 0 -6.909583333333334 0.09008333333333333 -8.3375 0.1566875 -0.7580416666666667 0.035458333333333335 -1.3929375000000002 0.5409791666666667 -1.5065000000000002 1.2913541666666666C1.5429166666666667 8.899083333333333 1.4375 9.997333333333334 1.4375 11.5c0 1.5026666666666668 0.10589583333333334 2.6013958333333336 0.21850000000000003 3.343625 0.1135625 0.750375 0.7484583333333333 1.2554166666666668 1.5065000000000002 1.2913541666666666 1.4279166666666667 0.06708333333333334 4.1275416666666676 0.1566875 8.3375 0.1566875 4.209958333333334 0 6.909583333333334 -0.09008333333333333 8.3375 -0.1566875 0.7580416666666667 -0.035458333333333335 1.3929375000000002 -0.5409791666666667 1.5065000000000002 -1.2913541666666666 0.11260416666666666 -0.7427083333333334 0.21850000000000003 -1.8409583333333335 0.21850000000000003 -3.343625 0 -1.5026666666666668 -0.10589583333333334 -2.6013958333333336 -0.21850000000000003 -3.343625Z" strokeWidth="1"></path>
												<path id="hover" fill="" d="M11.494250000000001 3.3033750000000004c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.917125 -0.9430000000000001 0.9497083333333334a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 0.043125c0.03833333333333334 1.3349583333333335 0.07666666666666667 3.5918333333333337 0.07666666666666667 7.2037916666666675 0 3.6124375 -0.0388125 5.869791666666667 -0.07666666666666667 7.2033125 0.4317291666666667 0.011020833333333334 0.7748125 0.026833333333333334 1.0445833333333334 0.043125 0.5337916666666668 0.03354166666666667 0.9276666666666666 0.4154375 0.9430000000000001 0.9501875000000001a15.218333333333335 15.218333333333335 0 0 1 0 0.85675c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.9166458333333334 -0.9430000000000001 0.9497083333333334 -0.5079166666666667 0.031625 -1.2769791666666668 0.059416666666666666 -2.4054166666666665 0.059416666666666666 -1.1279583333333334 0 -1.8975 -0.028270833333333332 -2.4054166666666665 -0.059416666666666666 -0.5337916666666668 -0.0330625 -0.9276666666666666 -0.4149583333333333 -0.9430000000000001 -0.9497083333333334a15.218333333333335 15.218333333333335 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.9166458333333334 0.9430000000000001 -0.9497083333333334a29.243062500000004 29.243062500000004 0 0 1 1.0445833333333334 -0.043125C6.746666666666667 17.36883333333333 6.708333333333334 15.111958333333334 6.708333333333334 11.5c0 -3.6124375 0.0388125 -5.869791666666667 0.07666666666666667 -7.2033125a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 -0.043125c-0.5337916666666668 -0.03354166666666667 -0.9276666666666666 -0.4154375 -0.9430000000000001 -0.9501875000000001a15.217854166666667 15.217854166666667 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.917125 0.9430000000000001 -0.9497083333333334C6.248333333333333 1.4652916666666667 7.0173958333333335 1.4375 8.145833333333334 1.4375c1.1284375 0 1.8975 0.02779166666666667 2.4054166666666665 0.059416666666666666 0.5337916666666668 0.03258333333333334 0.9276666666666666 0.4149583333333333 0.9430000000000001 0.9497083333333334a15.217854166666667 15.217854166666667 0 0 1 0 0.85675Z" strokeWidth="1"></path>
												<path id="Rectangle 1096" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M11.979166666666668 6.7088125c3.6565208333333334 0.006229166666666667 6.116083333333333 0.0805 7.547354166666667 0.1423125 0.9482708333333334 0.04120833333333333 1.7384166666666667 0.6732291666666667 1.8610833333333334 1.6143125000000003 0.09487500000000001 0.7268958333333333 0.17489583333333333 1.7283541666666669 0.17489583333333333 3.0345625000000003 0 1.3066875 -0.08002083333333335 2.3076666666666665 -0.17489583333333333 3.0345625000000003 -0.12266666666666667 0.9410833333333334 -0.9128125 1.5731041666666667 -1.8610833333333334 1.6147916666666668 -1.4312708333333335 0.061812500000000006 -3.890833333333333 0.13608333333333333 -7.547354166666667 0.14183333333333334" strokeWidth="1"></path>
												<path id="Rectangle 1098" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M4.3125 6.8180625c-0.31145833333333334 0.011020833333333334 -0.5908125000000001 0.022041666666666668 -0.8390208333333333 0.0330625 -0.9482708333333334 0.04120833333333333 -1.7384166666666667 0.6732291666666667 -1.8610833333333334 1.6143125000000003C1.5175208333333332 9.192333333333334 1.4375 10.193312500000001 1.4375 11.5c0 1.3066875 0.08002083333333335 2.3076666666666665 0.17489583333333333 3.0345625000000003 0.12266666666666667 0.9410833333333334 0.9128125 1.5731041666666667 1.8610833333333334 1.6143125000000003 0.24820833333333336 0.011020833333333334 0.5275625 0.022041666666666668 0.8390208333333333 0.0330625" strokeWidth="1"></path>
												<path id="Union_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M11.494250000000001 3.3033750000000004c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.917125 -0.9430000000000001 0.9497083333333334a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 0.043125c0.03833333333333334 1.3349583333333335 0.07666666666666667 3.5918333333333337 0.07666666666666667 7.2037916666666675 0 3.6124375 -0.0388125 5.869791666666667 -0.07666666666666667 7.2033125 0.4317291666666667 0.011020833333333334 0.7748125 0.026833333333333334 1.0445833333333334 0.043125 0.5337916666666668 0.03354166666666667 0.9276666666666666 0.4154375 0.9430000000000001 0.9501875000000001a15.218333333333335 15.218333333333335 0 0 1 0 0.85675c-0.015333333333333334 0.5347500000000001 -0.40920833333333334 0.9166458333333334 -0.9430000000000001 0.9497083333333334 -0.5079166666666667 0.031625 -1.2769791666666668 0.059416666666666666 -2.4054166666666665 0.059416666666666666 -1.1279583333333334 0 -1.8975 -0.028270833333333332 -2.4054166666666665 -0.059416666666666666 -0.5337916666666668 -0.0330625 -0.9276666666666666 -0.4149583333333333 -0.9430000000000001 -0.9497083333333334a15.218333333333335 15.218333333333335 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.9166458333333334 0.9430000000000001 -0.9497083333333334a29.243062500000004 29.243062500000004 0 0 1 1.0445833333333334 -0.043125C6.746666666666667 17.36883333333333 6.708333333333334 15.111958333333334 6.708333333333334 11.5c0 -3.6124375 0.0388125 -5.869791666666667 0.07666666666666667 -7.2033125a29.267500000000002 29.267500000000002 0 0 1 -1.0445833333333334 -0.043125c-0.5337916666666668 -0.03354166666666667 -0.9276666666666666 -0.4154375 -0.9430000000000001 -0.9501875000000001a15.217854166666667 15.217854166666667 0 0 1 0 -0.85675c0.015333333333333334 -0.5347500000000001 0.40920833333333334 -0.917125 0.9430000000000001 -0.9497083333333334C6.248333333333333 1.4652916666666667 7.0173958333333335 1.4375 8.145833333333334 1.4375c1.1284375 0 1.8975 0.02779166666666667 2.4054166666666665 0.059416666666666666 0.5337916666666668 0.03258333333333334 0.9276666666666666 0.4149583333333333 0.9430000000000001 0.9497083333333334a15.217854166666667 15.217854166666667 0 0 1 0 0.85675Z" strokeWidth="1"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Manual Keyword Research</span>
									</NavLink>
								</li>

								{/* ------------------------- ICP Keyword Research  ------------------------- */}
								<li className="mt-2 subcategory-menu">
									<NavLink to='/keyword-research?page=icp-keyword'
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" id="Target-3--Streamline-Sharp" height="24" width="24">
											<g id="target-3--shop-bullseye-shooting-target">
												<path id="ellipse 169" stroke="#000000" d="M2 12a10 10 0 1 0 20 0 10 10 0 1 0 -20 0" stroke-width="1.5"></path>
												<path id="Vector 2983" stroke="#000000" d="M12 9v6" stroke-width="1.5"></path>
												<path id="Vector 2985" stroke="#000000" d="M12 2v4" stroke-width="1.5"></path>
												<path id="Vector 2987" stroke="#000000" d="m22 12 -4 0" stroke-width="1.5"></path>
												<path id="Vector 2986" stroke="#000000" d="M12 18v4" stroke-width="1.5"></path>
												<path id="Vector 2988" stroke="#000000" d="m6 12 -4 0" stroke-width="1.5"></path>
												<path id="Vector 2984" stroke="#000000" d="m15 12 -6 0" stroke-width="1.5"></path>
											</g>
										</svg>
										<span className="link-text ml-4">ICP to Keyword Research</span>
									</NavLink>
								</li>

								{!isProduction && (
									<li className="mt-2 articles subcategory-menu" >
										<NavLink to='/optimize-publish-article-steps' className={({ isActive }) => isActive ? "is-active" : ""}
											onClick={(e) => {
												checkForUnsavedChanges(e);
												setHamburgerActive(false);
											}}>
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Application-Add--Streamline-Plump" height="48" width="48">
												<g id="application-add--application-new-add-square">
													<path id="Rectangle 123" stroke="#000000" stroke-linejoin="round" d="M3.135 6.597c0.104 -1.723 1.226 -3.058 2.937 -3.28C7.394 3.143 9.304 3 12 3c2.696 0 4.606 0.144 5.928 0.316 1.712 0.224 2.833 1.558 2.937 3.281C20.94 7.83 21 9.573 21 12c0 2.427 -0.06 4.17 -0.135 5.403 -0.104 1.723 -1.225 3.058 -2.937 3.28 -1.322 0.173 -3.232 0.317 -5.928 0.317 -2.696 0 -4.606 -0.144 -5.928 -0.316 -1.711 -0.224 -2.833 -1.558 -2.937 -3.281C3.06 16.17 3 14.427 3 12c0 -2.427 0.06 -4.17 0.135 -5.403Z" stroke-width="3"></path>
													<path id="Rectangle 123_2" stroke="#000000" stroke-linejoin="round" d="M3.135 41.403c0.104 1.723 1.226 3.058 2.937 3.28C7.394 44.857 9.304 45 12 45c2.696 0 4.606 -0.144 5.928 -0.316 1.712 -0.223 2.833 -1.558 2.937 -3.281C20.94 40.17 21 38.427 21 36c0 -2.427 -0.06 -4.17 -0.135 -5.403 -0.104 -1.723 -1.225 -3.058 -2.937 -3.28C16.606 27.143 14.696 27 12 27c-2.696 0 -4.606 0.144 -5.928 0.316 -1.711 0.224 -2.833 1.558 -2.937 3.281C3.06 31.83 3 33.573 3 36c0 2.427 0.06 4.17 0.135 5.403Z" stroke-width="3"></path>
													<path id="Rectangle 123_3" stroke="#000000" stroke-linejoin="round" d="M41.403 3.135c1.723 0.104 3.058 1.226 3.28 2.937C44.857 7.394 45 9.304 45 12c0 2.696 -0.144 4.606 -0.316 5.928 -0.223 1.712 -1.558 2.833 -3.281 2.937C40.17 20.94 38.427 21 36 21c-2.427 0 -4.17 -0.06 -5.403 -0.135 -1.723 -0.104 -3.058 -1.225 -3.28 -2.937C27.143 16.606 27 14.696 27 12c0 -2.696 0.144 -4.606 0.316 -5.928 0.224 -1.711 1.558 -2.833 3.281 -2.937C31.83 3.06 33.573 3 36 3c2.427 0 4.17 0.06 5.403 0.135Z" stroke-width="3"></path>
													<path id="Union" stroke="#000000" stroke-linejoin="round" d="M34.786 44.943c-1.017 -0.102 -1.647 -0.966 -1.686 -1.988 -0.033 -0.874 -0.067 -2.154 -0.086 -3.97a145.326 145.326 0 0 1 -3.969 -0.085c-1.022 -0.04 -1.886 -0.669 -1.988 -1.686A12.173 12.173 0 0 1 27 36c0 -0.468 0.022 -0.871 0.057 -1.214 0.102 -1.017 0.966 -1.647 1.988 -1.686 0.874 -0.033 2.154 -0.067 3.97 -0.086 0.018 -1.815 0.052 -3.095 0.085 -3.969 0.04 -1.022 0.669 -1.886 1.686 -1.988 0.343 -0.035 0.746 -0.057 1.214 -0.057 0.468 0 0.871 0.022 1.214 0.057 1.017 0.102 1.647 0.966 1.686 1.988 0.033 0.874 0.067 2.154 0.086 3.97 1.815 0.018 3.095 0.052 3.969 0.085 1.022 0.04 1.886 0.669 1.988 1.686 0.035 0.343 0.057 0.746 0.057 1.214 0 0.468 -0.022 0.871 -0.057 1.214 -0.102 1.017 -0.966 1.647 -1.988 1.686 -0.874 0.033 -2.154 0.067 -3.97 0.086a145.326 145.326 0 0 1 -0.085 3.969c-0.04 1.022 -0.669 1.886 -1.686 1.988 -0.343 0.035 -0.746 0.057 -1.214 0.057 -0.468 0 -0.871 -0.022 -1.214 -0.057Z" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Article Updater</span>
										</NavLink>
									</li>)}


								{/* ------------------------- Steal Competitor Keyword  ------------------------- */}
								<li className="mt-2 subcategory-menu">
									<NavLink to={pageURL['competitorResearch']} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Magnet--Streamline-Plump" height="48" width="48">
											<g id="magnet--design-magnet-snap-supplies-to-tool">
												<path id="Rectangle 650 (Stroke)" fill="" fill-rule="evenodd" d="M26.0802 4.15094c-0.7355 -0.75281 -1.8465 -0.9333 -2.8177 -0.52783 -3.1284 1.306 -6.1115 2.96638 -8.9108 4.86545C3.04011 16.1625 -1.66647 28.6388 8.93467 39.3373 19.1918 49.6887 32.2741 44.6366 39.5146 33.921c1.9065 -2.8216 3.5981 -5.8847 4.8994 -9.0719 0.3835 -0.9392 0.2177 -2.0096 -0.5087 -2.7179 -1.3581 -1.3242 -3.9431 -3.3686 -7.5476 -4.1343 -1.1086 -0.2355 -2.1973 0.349 -2.7883 1.316 -1.1165 1.827 -2.9604 4.8122 -4.165 6.595 -3.5372 5.2348 -8.413 7.4634 -11.4855 4.3627 -3.0725 -3.1008 -1.1213 -8.0307 4.2441 -11.6706 1.8392 -1.2478 4.9877 -3.1318 6.8042 -4.204 0.8752 -0.5165 1.4694 -1.442 1.3372 -2.4496 -0.452 -3.44378 -2.7559 -6.29265 -4.2242 -7.79546Z" clip-rule="evenodd" stroke-width="3"></path>
												<path id="Intersect" fill="" d="M41.4222 30.8888c1.1307 -1.9387 2.1443 -3.9626 2.9921 -6.0391 0.3835 -0.9393 0.2177 -2.0097 -0.5087 -2.7179 -1.3581 -1.3242 -3.9431 -3.3687 -7.5476 -4.1344 -1.1086 -0.2355 -2.1973 0.349 -2.7883 1.316 -0.7927 1.2971 -1.952 3.1781 -2.9889 4.798 3.705 3.2883 8.1406 5.5672 10.8414 6.7774Z" stroke-width="3"></path>
												<path id="Intersect_2" fill="" d="M24.0621 17.3686c1.6799 -1.0559 3.6241 -2.2163 4.9055 -2.9726 0.8752 -0.5165 1.4694 -1.442 1.3372 -2.4496 -0.452 -3.44378 -2.7559 -6.29265 -4.2242 -7.79546 -0.7355 -0.75281 -1.8465 -0.9333 -2.8177 -0.52783 -1.9187 0.80099 -3.7827 1.73528 -5.5832 2.77618 1.1038 2.63041 3.2505 7.12571 6.3824 10.96931Z" stroke-width="3"></path>
												<path id="Rectangle 650 (Stroke)_2" fill-rule="evenodd" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M26.0802 4.15094c-0.7355 -0.75281 -1.8465 -0.9333 -2.8177 -0.52783 -3.1284 1.306 -6.1115 2.96638 -8.9108 4.86545C3.04011 16.1625 -1.66647 28.6388 8.93467 39.3373 19.1918 49.6887 32.2741 44.6366 39.5146 33.921c1.9065 -2.8216 3.5981 -5.8847 4.8994 -9.0719 0.3835 -0.9392 0.2177 -2.0096 -0.5087 -2.7179 -1.3581 -1.3242 -3.9431 -3.3686 -7.5476 -4.1343 -1.1086 -0.2355 -2.1973 0.349 -2.7883 1.316 -1.1165 1.827 -2.9604 4.8122 -4.165 6.595 -3.5372 5.2348 -8.413 7.4634 -11.4855 4.3627 -3.0725 -3.1008 -1.1213 -8.0307 4.2441 -11.6706 1.8392 -1.2478 4.9877 -3.1318 6.8042 -4.204 0.8752 -0.5165 1.4694 -1.442 1.3372 -2.4496 -0.452 -3.44378 -2.7559 -6.29265 -4.2242 -7.79546Z" clip-rule="evenodd" stroke-width="3"></path>
												<path id="Intersect_3" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M41.4219 30.8884c-2.7008 -1.2102 -7.1365 -3.4891 -10.8415 -6.7774m-6.5188 -6.7422c-3.1319 -3.8437 -5.2786 -8.33898 -6.3824 -10.96939" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Steal Competitor Keywords</span>
									</NavLink>
								</li>

								{/* ------------------------- Longtail Keyword Research  ------------------------- */}
								<li className="mt-2 subcategory-menu">
									<NavLink to='/keyword-research?page=longtail'
										className={({ isActive }) =>
											isActive && location.search.includes("page=longtail") ? 'is-active' : ''
										}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>

										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Auto-Flash--Streamline-Plump" height="48" width="48">
											<g id="auto-flash">
												<path id="Union" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M12.029 25.19a61.52 61.52 0 0 1 -7.47 -1.543c-1.852 -0.512 -2.496 -2.64 -1.341 -4.177C6.004 15.762 11.1 9.234 16.666 3.34c1.54 -1.628 4.14 -0.45 3.97 1.785 -0.352 4.576 -0.798 8.078 -1.107 11.161a47.233 47.233 0 0 1 8.203 1.943c1.749 0.589 2.296 2.64 1.19 4.116 -2.755 3.672 -7.929 10.322 -13.59 16.315 -1.538 1.63 -4.167 0.45 -4.062 -1.789 0.225 -4.776 0.448 -8.575 0.759 -11.68Z" stroke-width="3"></path>
												<path id="Vector 1" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m28 45 2.133 -6m0 0 3.761 -10.577C34.196 27.573 35.044 27 36 27s1.804 0.573 2.106 1.423L41.866 39m-11.733 0h11.734m0 0L44 45" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Longtail Keyword Research</span>
									</NavLink>
								</li>

								{/* ------------------------- CONTENT AUTOMATION ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu">
									{props.currentPlanDisplayName.includes("Trial") ?
										props.hasLtdPlan ? null : (
											<NavLink to={pageURL['addAutomation']} className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
												onClick={(e) => {
													checkForUnsavedChanges(e);
													setHamburgerActive(false);
												}}>
												<div className="category">
													<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="48" width="48">
														<g>
															<path id="Subtract" fill="" d="M2.4035047916666668 14.614104166666667C2.3985310416666668 14.233070833333334 2.3958333333333335 13.834020833333334 2.3958333333333335 13.416666666666668c0 -2.5854395833333332 0.10343770833333334 -4.467510416666666 0.21182041666666668 -5.729922916666667 0.10471708333333335 -1.2196708333333333 1.0569122916666667 -2.1305666666666667 2.2783129166666667 -2.2130791666666667C6.3641958333333335 5.373854166666667 8.687004166666668 5.270833333333334 11.979166666666668 5.270833333333334c3.2921625 0 5.6149708333333335 0.10302083333333334 7.0932 0.20283125000000002 0.36651458333333337 0.02477291666666667 0.7087354166666667 0.12410416666666668 1.0111375 0.2840020833333333 -0.16406666666666667 -0.9921912500000001 -0.31955625000000004 -1.8018870833333334 -0.45463333333333333 -2.442585625 -0.24509375 -1.1622283333333334 -1.2750625 -1.9346210416666667 -2.47408125 -1.8742795833333334 -1.4488083333333335 0.07291479166666667 -3.7170395833333334 0.24217083333333334 -6.912266666666667 0.6301377083333334 -3.1951791666666667 0.387966875 -5.437439583333333 0.7663791666666666 -6.8610581250000005 1.0422402083333335 -1.1781510416666667 0.22829416666666666 -1.9886087500000003 1.2241558333333333 -1.9420577083333335 2.4105102083333336 0.0508875 1.2969125 0.1864485416666667 3.2579979166666666 0.5223347916666667 5.963229166666667 0.14749229166666666 1.1879020833333334 0.29736125 2.2273104166666666 0.44176291666666667 3.127185416666667Z" strokeWidth="1." />
															<path id="hover" fill="" d="M39.056 16.6c1.565 0.176 2.703 1.401 2.822 2.971 0.136 1.796 0.216 4.243 -0.08 6.366a2.956 2.956 0 0 1 -0.969 1.806c-1.23 1.093 -3.931 3.217 -8.238 5.179a198.938 198.938 0 0 1 -0.382 3.084 1.905 1.905 0 0 1 -1.71 1.655C28.973 37.807 26.487 38 24 38c-2.486 0 -4.973 -0.193 -6.499 -0.34a1.905 1.905 0 0 1 -1.71 -1.654c-0.099 -0.747 -0.229 -1.775 -0.382 -3.084 -4.307 -1.962 -7.009 -4.086 -8.238 -5.179a2.956 2.956 0 0 1 -0.968 -1.806c-0.297 -2.123 -0.217 -4.57 -0.081 -6.366 0.119 -1.57 1.257 -2.795 2.822 -2.971 2.478 -0.28 7.069 -0.6 15.056 -0.6 7.987 0 12.578 0.322 15.056 0.6Z" strokeWidth="3" />
															<path id="Rectangle 1103" fill="" d="M12.021045833333334 13.810781250000002c0.04120833333333333 0.8179375000000001 0.6627354166666667 1.4394645833333335 1.4806729166666668 1.4806729166666668C13.97638125 15.315364583333333 14.582910416666667 15.333333333333334 15.333333333333334 15.333333333333334c0.7504229166666667 0 1.3569520833333335 -0.01796875 1.8316145833333333 -0.04187916666666667 0.8179375000000001 -0.04120833333333333 1.4394645833333335 -0.6627354166666667 1.4806729166666668 -1.4806729166666668C18.66953125 13.33611875 18.6875 12.729589583333333 18.6875 11.979166666666668c0 -0.7504229166666667 -0.01796875 -1.3569520833333335 -0.04187916666666667 -1.8316145833333333 -0.04120833333333333 -0.8179375000000001 -0.6627354166666667 -1.4394645833333335 -1.4806729166666668 -1.4806729166666668C16.690285416666665 8.642968750000001 16.08375625 8.625 15.333333333333334 8.625c-0.7504229166666667 0 -1.3569520833333335 0.01796875 -1.8316145833333333 0.04187916666666667 -0.8179375000000001 0.04120833333333333 -1.4394645833333335 0.6627354166666667 -1.4806729166666668 1.4806729166666668C11.997135416666667 10.622214583333333 11.979166666666668 11.228743750000001 11.979166666666668 11.979166666666668c0 0.7504229166666667 0.01796875 1.3569520833333335 0.04187916666666667 1.8316145833333333Z" strokeWidth="1.5" />
															<path id="Rectangle 1100" fill="" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M19.608697916666667 3.3036193750000002c-0.24475833333333336 -1.1551366666666667 -1.273625 -1.9228095833333332 -2.4713020833333332 -1.8628370833333334 -1.4472270833333334 0.072464375 -3.7129187500000005 0.24069020833333335 -6.9046 0.62629 -3.1916812500000002 0.38559979166666664 -5.431450000000001 0.7616977083333334 -6.8534825 1.035876875 -1.1768477083333335 0.22689979166666668 -1.9864045833333333 1.216685625 -1.939911041666667 2.3957758333333334 0.05083479166666667 1.2890541666666666 0.18624250000000003 3.2381124999999997 0.5217597916666666 5.926860416666667 0.15179041666666668 1.2164125000000001 0.30610604166666666 2.276185416666667 0.4543697916666667 3.188997916666667" strokeWidth="1.5" />
															<path id="Rectangle 1099" stroke="#000000" strokeLinejoin="round" d="M2.60765375 19.146589583333334c0.10471229166666667 1.2196708333333333 1.0569122916666667 2.1305666666666667 2.2783129166666667 2.2130791666666667C6.3641958333333335 21.459479166666664 8.687004166666668 21.5625 11.979166666666668 21.5625c3.2921625 0 5.6149708333333335 -0.10302083333333334 7.0932 -0.20283125000000002 1.2213958333333335 -0.0825125 2.1735958333333336 -0.9934083333333333 2.27829375 -2.2130791666666667C21.45904791666667 17.884177083333334 21.5625 16.00210625 21.5625 13.416666666666668s-0.10345208333333335 -4.467510416666666 -0.21183958333333333 -5.729922916666667c-0.10469791666666667 -1.2196708333333333 -1.0568979166666668 -2.1305666666666667 -2.27829375 -2.2130791666666667C17.594137500000002 5.373854166666667 15.271329166666668 5.270833333333334 11.979166666666668 5.270833333333334c-3.2921625 0 -5.6149708333333335 0.10302083333333334 -7.0932 0.20283125000000002 -1.221400625 0.0825125 -2.1735958333333336 0.9934083333333333 -2.2783129166666667 2.2130791666666667C2.499271041666667 8.949156250000001 2.3958333333333335 10.831227083333333 2.3958333333333335 13.416666666666668s0.10343770833333334 4.467510416666666 0.21182041666666668 5.729922916666667Z" strokeWidth="1.5" />
															<path id="Vector 1469" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M9.104166666666668 9.104166666666668h-3.8333333333333335" strokeWidth="1.5" />
															<path id="Vector 1471" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M9.104166666666668 14.854166666666668h-3.8333333333333335" strokeWidth="1.5" />
															<path id="Vector 1472" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M18.6875 17.729166666666668H5.270833333333334" strokeWidth="1.5" />
															<path id="Vector 1470" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M9.104166666666668 11.979166666666668h-3.8333333333333335" strokeWidth="1.5" />
															<path id="Rectangle 1096" fill="" stroke="#000000" strokeLinejoin="round" d="M12.021045833333334 13.810781250000002c0.04120833333333333 0.8179375000000001 0.6627354166666667 1.4394645833333335 1.4806729166666668 1.4806729166666668C13.97638125 15.315364583333333 14.582910416666667 15.333333333333334 15.333333333333334 15.333333333333334c0.7504229166666667 0 1.3569520833333335 -0.01796875 1.8316145833333333 -0.04187916666666667 0.8179375000000001 -0.04120833333333333 1.4394645833333335 -0.6627354166666667 1.4806729166666668 -1.4806729166666668C18.66953125 13.33611875 18.6875 12.729589583333333 18.6875 11.979166666666668c0 -0.7504229166666667 -0.01796875 -1.3569520833333335 -0.04187916666666667 -1.8316145833333333 -0.04120833333333333 -0.8179375000000001 -0.6627354166666667 -1.4394645833333335 -1.4806729166666668 -1.4806729166666668C16.690285416666665 8.642968750000001 16.08375625 8.625 15.333333333333334 8.625c-0.7504229166666667 0 -1.3569520833333335 0.01796875 -1.8316145833333333 0.04187916666666667 -0.8179375000000001 0.04120833333333333 -1.4394645833333335 0.6627354166666667 -1.4806729166666668 1.4806729166666668C11.997135416666667 10.622214583333333 11.979166666666668 11.228743750000001 11.979166666666668 11.979166666666668c0 0.7504229166666667 0.01796875 1.3569520833333335 0.04187916666666667 1.8316145833333333Z" strokeWidth="1.5" />
														</g>
													</svg>
													<span className="link-text ml-4">
														Auto Blog
													</span>
												</div>
												{/* <span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu">
												Upgrade ⚡
											</span> */}
											</NavLink>
										) : (
											<NavLink to={pageURL['addAutomation']}
												className={({ isActive }) => isActive ? `is-active` : ""}
												onClick={(e) => {
													checkForUnsavedChanges(e);
													setHamburgerActive(false);
												}}>
												<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
													<g>
														<path id="Subtract" fill="" d="M2.4035047916666668 14.614104166666667C2.3985310416666668 14.233070833333334 2.3958333333333335 13.834020833333334 2.3958333333333335 13.416666666666668c0 -2.5854395833333332 0.10343770833333334 -4.467510416666666 0.21182041666666668 -5.729922916666667 0.10471708333333335 -1.2196708333333333 1.0569122916666667 -2.1305666666666667 2.2783129166666667 -2.2130791666666667C6.3641958333333335 5.373854166666667 8.687004166666668 5.270833333333334 11.979166666666668 5.270833333333334c3.2921625 0 5.6149708333333335 0.10302083333333334 7.0932 0.20283125000000002 0.36651458333333337 0.02477291666666667 0.7087354166666667 0.12410416666666668 1.0111375 0.2840020833333333 -0.16406666666666667 -0.9921912500000001 -0.31955625000000004 -1.8018870833333334 -0.45463333333333333 -2.442585625 -0.24509375 -1.1622283333333334 -1.2750625 -1.9346210416666667 -2.47408125 -1.8742795833333334 -1.4488083333333335 0.07291479166666667 -3.7170395833333334 0.24217083333333334 -6.912266666666667 0.6301377083333334 -3.1951791666666667 0.387966875 -5.437439583333333 0.7663791666666666 -6.8610581250000005 1.0422402083333335 -1.1781510416666667 0.22829416666666666 -1.9886087500000003 1.2241558333333333 -1.9420577083333335 2.4105102083333336 0.0508875 1.2969125 0.1864485416666667 3.2579979166666666 0.5223347916666667 5.963229166666667 0.14749229166666666 1.1879020833333334 0.29736125 2.2273104166666666 0.44176291666666667 3.127185416666667Z" strokeWidth="1." />
														<path id="Rectangle 1103" fill="" d="M12.021045833333334 13.810781250000002c0.04120833333333333 0.8179375000000001 0.6627354166666667 1.4394645833333335 1.4806729166666668 1.4806729166666668C13.97638125 15.315364583333333 14.582910416666667 15.333333333333334 15.333333333333334 15.333333333333334c0.7504229166666667 0 1.3569520833333335 -0.01796875 1.8316145833333333 -0.04187916666666667 0.8179375000000001 -0.04120833333333333 1.4394645833333335 -0.6627354166666667 1.4806729166666668 -1.4806729166666668C18.66953125 13.33611875 18.6875 12.729589583333333 18.6875 11.979166666666668c0 -0.7504229166666667 -0.01796875 -1.3569520833333335 -0.04187916666666667 -1.8316145833333333 -0.04120833333333333 -0.8179375000000001 -0.6627354166666667 -1.4394645833333335 -1.4806729166666668 -1.4806729166666668C16.690285416666665 8.642968750000001 16.08375625 8.625 15.333333333333334 8.625c-0.7504229166666667 0 -1.3569520833333335 0.01796875 -1.8316145833333333 0.04187916666666667 -0.8179375000000001 0.04120833333333333 -1.4394645833333335 0.6627354166666667 -1.4806729166666668 1.4806729166666668C11.997135416666667 10.622214583333333 11.979166666666668 11.228743750000001 11.979166666666668 11.979166666666668c0 0.7504229166666667 0.01796875 1.3569520833333335 0.04187916666666667 1.8316145833333333Z" strokeWidth="1.5" />
														<path id="Rectangle 1100" fill="" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M19.608697916666667 3.3036193750000002c-0.24475833333333336 -1.1551366666666667 -1.273625 -1.9228095833333332 -2.4713020833333332 -1.8628370833333334 -1.4472270833333334 0.072464375 -3.7129187500000005 0.24069020833333335 -6.9046 0.62629 -3.1916812500000002 0.38559979166666664 -5.431450000000001 0.7616977083333334 -6.8534825 1.035876875 -1.1768477083333335 0.22689979166666668 -1.9864045833333333 1.216685625 -1.939911041666667 2.3957758333333334 0.05083479166666667 1.2890541666666666 0.18624250000000003 3.2381124999999997 0.5217597916666666 5.926860416666667 0.15179041666666668 1.2164125000000001 0.30610604166666666 2.276185416666667 0.4543697916666667 3.188997916666667" strokeWidth="1.5" />
														<path id="Rectangle 1099" stroke="#000000" strokeLinejoin="round" d="M2.60765375 19.146589583333334c0.10471229166666667 1.2196708333333333 1.0569122916666667 2.1305666666666667 2.2783129166666667 2.2130791666666667C6.3641958333333335 21.459479166666664 8.687004166666668 21.5625 11.979166666666668 21.5625c3.2921625 0 5.6149708333333335 -0.10302083333333334 7.0932 -0.20283125000000002 1.2213958333333335 -0.0825125 2.1735958333333336 -0.9934083333333333 2.27829375 -2.2130791666666667C21.45904791666667 17.884177083333334 21.5625 16.00210625 21.5625 13.416666666666668s-0.10345208333333335 -4.467510416666666 -0.21183958333333333 -5.729922916666667c-0.10469791666666667 -1.2196708333333333 -1.0568979166666668 -2.1305666666666667 -2.27829375 -2.2130791666666667C17.594137500000002 5.373854166666667 15.271329166666668 5.270833333333334 11.979166666666668 5.270833333333334c-3.2921625 0 -5.6149708333333335 0.10302083333333334 -7.0932 0.20283125000000002 -1.221400625 0.0825125 -2.1735958333333336 0.9934083333333333 -2.2783129166666667 2.2130791666666667C2.499271041666667 8.949156250000001 2.3958333333333335 10.831227083333333 2.3958333333333335 13.416666666666668s0.10343770833333334 4.467510416666666 0.21182041666666668 5.729922916666667Z" strokeWidth="1.5" />
														<path id="Vector 1469" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M9.104166666666668 9.104166666666668h-3.8333333333333335" strokeWidth="1.5" />
														<path id="Vector 1471" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M9.104166666666668 14.854166666666668h-3.8333333333333335" strokeWidth="1.5" />
														<path id="Vector 1472" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M18.6875 17.729166666666668H5.270833333333334" strokeWidth="1.5" />
														<path id="Vector 1470" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M9.104166666666668 11.979166666666668h-3.8333333333333335" strokeWidth="1.5" />
														<path id="Rectangle 1096" fill="" stroke="#000000" strokeLinejoin="round" d="M12.021045833333334 13.810781250000002c0.04120833333333333 0.8179375000000001 0.6627354166666667 1.4394645833333335 1.4806729166666668 1.4806729166666668C13.97638125 15.315364583333333 14.582910416666667 15.333333333333334 15.333333333333334 15.333333333333334c0.7504229166666667 0 1.3569520833333335 -0.01796875 1.8316145833333333 -0.04187916666666667 0.8179375000000001 -0.04120833333333333 1.4394645833333335 -0.6627354166666667 1.4806729166666668 -1.4806729166666668C18.66953125 13.33611875 18.6875 12.729589583333333 18.6875 11.979166666666668c0 -0.7504229166666667 -0.01796875 -1.3569520833333335 -0.04187916666666667 -1.8316145833333333 -0.04120833333333333 -0.8179375000000001 -0.6627354166666667 -1.4394645833333335 -1.4806729166666668 -1.4806729166666668C16.690285416666665 8.642968750000001 16.08375625 8.625 15.333333333333334 8.625c-0.7504229166666667 0 -1.3569520833333335 0.01796875 -1.8316145833333333 0.04187916666666667 -0.8179375000000001 0.04120833333333333 -1.4394645833333335 0.6627354166666667 -1.4806729166666668 1.4806729166666668C11.997135416666667 10.622214583333333 11.979166666666668 11.228743750000001 11.979166666666668 11.979166666666668c0 0.7504229166666667 0.01796875 1.3569520833333335 0.04187916666666667 1.8316145833333333Z" strokeWidth="1.5" />
													</g>
												</svg>
												<span className="link-text ml-4">Auto Blog</span>
											</NavLink>
										)
									}
								</li>

								{/* ------------------------- PROGRAMMATIC SEO ------------------------- */}
								<li className="mt-2 articles subcategory-menu">
									<NavLink to={pageURL['programmaticSeo']} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" className="svg-reddit-icon" fill="none" viewBox="0 0 48 48" id="Polaroid--Streamline-Plump" height="48" width="48" >
											<g id="polaroid--photos-polaroid-picture-camera-photography-photo-pictures-image">
												<path id="Subtract" fill="" d="M12.8947 39.1969c0.0631 -0.1699 0.2275 -0.2805 0.4087 -0.2753 1.6734 0.0482 3.5683 0.078 5.6962 0.078 4.9802 0 8.6841 -0.1632 11.2582 -0.3437 4.546 -0.3188 8.0794 -3.8522 8.3982 -8.3982 0.1805 -2.5741 0.3436 -6.278 0.3436 -11.2581 0 -1.8399 -0.0222 -3.5056 -0.0594 -5.0045 1.2025 0.3778 2.24 0.7214 3.1219 1.0253 2.4239 0.8351 3.8035 3.2247 3.3148 5.7414 -0.4729 2.4355 -1.2525 5.9644 -2.5189 10.6906 -1.2664 4.7263 -2.3557 8.1721 -3.1639 10.5178 -0.8351 2.4239 -3.2247 3.8035 -5.7414 3.3148 -2.4355 -0.4729 -5.9644 -1.2525 -10.6906 -2.5189 -4.459 -1.1947 -7.7783 -2.2319 -10.1093 -3.0241 -0.2245 -0.0763 -0.3406 -0.3228 -0.2581 -0.5451Z" stroke-width="3"></path>
												<path id="Subtract_2" fill="" d="M12.8945 39.1981c0.0629 -0.1704 0.2276 -0.2815 0.4092 -0.2763 1.6735 0.0482 3.5685 0.078 5.6966 0.078 4.9801 0 8.684 -0.1631 11.2581 -0.3436 0.9141 -0.0641 1.7873 -0.2582 2.6016 -0.5644l7.4632 1.9997c-0.2247 0.6915 -0.4347 1.3173 -0.6285 1.8797 -0.8351 2.4239 -3.2247 3.8035 -5.7414 3.3148 -2.4355 -0.4729 -5.9644 -1.2525 -10.6906 -2.5189 -4.4592 -1.1948 -7.7787 -2.232 -10.1097 -3.0242 -0.2243 -0.0763 -0.3406 -0.3225 -0.2585 -0.5448Z" stroke-width="3"></path>
												<path id="Rectangle 1096" fill="" d="M3.33388 29.9783c0.17934 2.5574 2.13038 4.5085 4.68782 4.6878C10.4966 34.8397 14.107 35 19 35c4.893 0 8.5034 -0.1603 10.9783 -0.3339 2.5574 -0.1793 4.5085 -2.1304 4.6878 -4.6878C34.8397 27.5034 35 23.893 35 19c0 -4.893 -0.1603 -8.5034 -0.3339 -10.9783 -0.1793 -2.55744 -2.1304 -4.50848 -4.6878 -4.68782C27.5034 3.16032 23.893 3 19 3c-4.893 0 -8.5034 0.16032 -10.9783 0.33387 -2.55744 0.17935 -4.50848 2.13039 -4.68782 4.68783C3.16032 10.4966 3 14.107 3 19c0 4.893 0.16032 8.5034 0.33388 10.9783Z" stroke-width="3"></path>
												<path id="Intersect" fill="" d="M8.02126 34.6661c-2.55744 -0.1793 -4.50849 -2.1304 -4.68783 -4.6878 -0.04162 -0.5935 -0.08247 -1.2522 -0.12054 -1.9783H34.7862c-0.0381 0.7261 -0.0789 1.3848 -0.1205 1.9783 -0.1794 2.5574 -2.1304 4.5085 -4.6879 4.6878 -2.4749 0.1736 -6.0853 0.3339 -10.9782 0.3339 -4.893 0 -8.5034 -0.1603 -10.97834 -0.3339Z" stroke-width="3"></path>
												<path id="Rectangle 1095" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M40 14.335c0.7646 0.2471 1.4512 0.477 2.0629 0.6878 2.4239 0.8352 3.8035 3.2247 3.3148 5.7414 -0.4729 2.4355 -1.2525 5.9644 -2.5189 10.6906 -1.2664 4.7263 -2.3557 8.1722 -3.1639 10.5178 -0.8351 2.4239 -3.2246 3.8035 -5.7413 3.3148 -2.4356 -0.4729 -5.9644 -1.2525 -10.6907 -2.5189 -4.2426 -1.1368 -7.4534 -2.1309 -9.7629 -2.907" stroke-width="3"></path>
												<path id="Vector 906" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M40.125 40.1875 34 38.5463" stroke-width="3"></path>
												<path id="Rectangle 1095_2" stroke="#000000" stroke-linejoin="round" d="M3.33388 29.9783c0.17934 2.5574 2.13038 4.5085 4.68782 4.6878C10.4966 34.8397 14.107 35 19 35c4.893 0 8.5034 -0.1603 10.9783 -0.3339 2.5574 -0.1793 4.5085 -2.1304 4.6878 -4.6878C34.8397 27.5034 35 23.893 35 19c0 -4.893 -0.1603 -8.5034 -0.3339 -10.9783 -0.1793 -2.55744 -2.1304 -4.50848 -4.6878 -4.68782C27.5034 3.16032 23.893 3 19 3c-4.893 0 -8.5034 0.16032 -10.9783 0.33387 -2.55744 0.17935 -4.50848 2.13039 -4.68782 4.68783C3.16032 10.4966 3 14.107 3 19c0 4.893 0.16032 8.5034 0.33388 10.9783Z" stroke-width="3"></path>
												<path id="Vector 906_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3.5 28h31" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Programmatic SEO</span>
									</NavLink>
								</li>

								{/* ------------------------- Article Content Schedule ------------------------- */}
								<li className="mt-2 articles subcategory-menu">
									<NavLink to={"/content-scheduler"} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Notepad-Text--Streamline-Plump" height="48" width="48">
											<g id="notepad-text--content-notes-book-notepad-notebook">
												<path id="hover" fill="" d="M39.745 44.543c2.553 -0.187 4.533 -2.113 4.75 -4.664 0.252 -2.939 0.505 -7.478 0.505 -13.848 0 -6.37 -0.253 -10.909 -0.504 -13.848 -0.218 -2.55 -2.198 -4.476 -4.75 -4.664 -3.229 -0.237 -8.38 -0.488 -15.746 -0.488s-12.517 0.25 -15.745 0.488c-2.554 0.188 -4.532 2.113 -4.75 4.664C3.252 15.123 3 19.662 3 26.031c0 6.37 0.253 10.91 0.504 13.848 0.219 2.551 2.197 4.477 4.75 4.664 3.229 0.238 8.38 0.488 15.746 0.488s12.517 -0.25 15.745 -0.488Z" stroke-width="3"></path>
												<path id="Rectangle 1101" fill="" d="M10.043 10.628c0.042 1.25 0.791 2.277 2.04 2.348 0.27 0.015 0.575 0.024 0.917 0.024 0.342 0 0.646 -0.009 0.916 -0.024 1.25 -0.07 2 -1.097 2.041 -2.348a78.576 78.576 0 0 0 0 -5.256c-0.042 -1.25 -0.791 -2.277 -2.04 -2.348a17.668 17.668 0 0 0 -1.833 0c-1.25 0.07 -2 1.097 -2.041 2.348a78.573 78.573 0 0 0 0 5.256Z" stroke-width="3"></path>
												<path id="Rectangle 1102" fill="" d="M21.043 10.628c0.042 1.25 0.791 2.277 2.04 2.348 0.27 0.015 0.575 0.024 0.917 0.024 0.342 0 0.646 -0.009 0.916 -0.024 1.25 -0.07 2 -1.097 2.041 -2.348a78.576 78.576 0 0 0 0 -5.256c-0.042 -1.25 -0.791 -2.277 -2.04 -2.348a17.668 17.668 0 0 0 -1.833 0c-1.25 0.07 -2 1.097 -2.041 2.348a78.573 78.573 0 0 0 0 5.256Z" stroke-width="3"></path>
												<path id="Rectangle 1103" fill="" d="M32.043 10.628c0.042 1.25 0.791 2.277 2.04 2.348 0.27 0.015 0.575 0.024 0.917 0.024 0.342 0 0.646 -0.009 0.916 -0.024 1.25 -0.07 2 -1.097 2.041 -2.348a78.576 78.576 0 0 0 0 -5.256c-0.042 -1.25 -0.791 -2.277 -2.04 -2.348a17.668 17.668 0 0 0 -1.833 0c-1.25 0.07 -2 1.097 -2.041 2.348a78.573 78.573 0 0 0 0 5.256Z" stroke-width="3"></path>
												<path id="Vector 829" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m15 25 18 0" stroke-width="3"></path>
												<path id="Vector 830" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M15 34h9" stroke-width="3"></path>
												<path id="Rectangle 1098" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M10.043 10.628c0.042 1.25 0.791 2.277 2.04 2.348 0.27 0.015 0.575 0.024 0.917 0.024 0.342 0 0.646 -0.009 0.916 -0.024 1.25 -0.07 2 -1.097 2.041 -2.348a78.575 78.575 0 0 0 0 -5.256c-0.042 -1.25 -0.791 -2.277 -2.04 -2.348a17.668 17.668 0 0 0 -1.833 0c-1.25 0.07 -2 1.097 -2.041 2.348a78.573 78.573 0 0 0 0 5.256Z" stroke-width="3"></path>
												<path id="Rectangle 1099" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M21.043 10.628c0.042 1.25 0.791 2.277 2.04 2.348 0.27 0.015 0.575 0.024 0.917 0.024 0.342 0 0.646 -0.009 0.916 -0.024 1.25 -0.07 2 -1.097 2.041 -2.348a78.575 78.575 0 0 0 0 -5.256c-0.042 -1.25 -0.791 -2.277 -2.04 -2.348a17.668 17.668 0 0 0 -1.833 0c-1.25 0.07 -2 1.097 -2.041 2.348a78.573 78.573 0 0 0 0 5.256Z" stroke-width="3"></path>
												<path id="Rectangle 1100" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M32.043 10.628c0.042 1.25 0.791 2.277 2.04 2.348 0.27 0.015 0.575 0.024 0.917 0.024 0.342 0 0.646 -0.009 0.916 -0.024 1.25 -0.07 2 -1.097 2.041 -2.348a78.575 78.575 0 0 0 0 -5.256c-0.042 -1.25 -0.791 -2.277 -2.04 -2.348a17.668 17.668 0 0 0 -1.833 0c-1.25 0.07 -2 1.097 -2.041 2.348a78.573 78.573 0 0 0 0 5.256Z" stroke-width="3"></path>
												<path id="Subtract" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M10.002 7.371c-0.635 0.039 -1.217 0.078 -1.747 0.117 -2.554 0.188 -4.532 2.113 -4.75 4.664C3.252 15.092 3 19.631 3 26c0 6.37 0.253 10.909 0.504 13.848 0.219 2.551 2.197 4.477 4.75 4.664C11.484 44.75 16.635 45 24 45s12.517 -0.25 15.745 -0.488c2.553 -0.187 4.533 -2.113 4.75 -4.664C44.748 36.908 45 32.37 45 26c0 -6.37 -0.253 -10.909 -0.504 -13.848 -0.218 -2.55 -2.198 -4.476 -4.75 -4.664 -0.531 -0.04 -1.113 -0.078 -1.748 -0.117" stroke-width="3"></path>
												<path id="Subtract_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M21.006 7.014c-1.828 0.018 -3.496 0.051 -5.01 0.095" stroke-width="3"></path>
												<path id="Subtract_3" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M32.005 7.11a260.984 260.984 0 0 0 -5.01 -0.096" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Content Calendar</span>
									</NavLink>
								</li>

								{/* ------------------------- GSC Analytics ------------------------- */}
								{!isProduction && (
									<li className="mt-2 autoArticles subcategory-menu">
										{props.currentPlanName === "Trial" ?
											<NavLink to={pageURL['GSCAnalytics']} className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
												onClick={(e) => {
													checkForUnsavedChanges(e);
													setHamburgerActive(false);
												}}>
												<div className="category">
													<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Content-Statistic--Streamline-Plump" height="48" width="48">
														<g id="content-statistic--product-data-analysis-analytics-graph-line-business-board-chart">
															<path id="Rectangle 1097" fill="" d="M39.743 44.461c2.555 -0.208 4.51 -2.163 4.718 -4.718C44.723 36.515 45 31.364 45 24c0 -7.364 -0.277 -12.515 -0.539 -15.743 -0.208 -2.555 -2.163 -4.51 -4.718 -4.718C36.515 3.277 31.364 3 24 3c-7.364 0 -12.515 0.277 -15.743 0.539 -2.555 0.208 -4.51 2.163 -4.718 4.718C3.277 11.485 3 16.636 3 24c0 7.364 0.277 12.515 0.539 15.743 0.208 2.555 2.163 4.51 4.718 4.718C11.485 44.723 16.636 45 24 45c7.364 0 12.515 -0.277 15.743 -0.539Z" stroke-width="3"></path>
															<path id="Vector 79 (Stroke)" fill="" fill-rule="evenodd" d="M31.659 34.651a3.718 3.718 0 0 1 -4.973 0.525 180.782 180.782 0 0 1 -4.941 -3.73 0.98 0.98 0 0 0 -1.402 0.184c-1.126 1.552 -2.378 3.507 -3.384 5.137 -1.174 1.9 -3.487 2.876 -5.345 1.636a12.006 12.006 0 0 1 -1.323 -1.021c-1.577 -1.395 -1.414 -3.735 -0.047 -5.335 2.15 -2.517 4.727 -5.362 7.225 -7.79 1.458 -1.417 3.752 -1.366 5.274 -0.017 1.521 1.348 3.428 2.987 4.957 4.138a0.952 0.952 0 0 0 1.274 -0.116c1.721 -1.853 4.116 -4.704 5.619 -6.52 0.916 -1.108 2.286 -1.858 3.51 -1.104 0.29 0.18 0.571 0.387 0.824 0.61 0.947 0.838 0.778 2.193 0.143 3.286 -1.212 2.087 -3.635 5.874 -7.411 10.117Z" clip-rule="evenodd" stroke-width="3"></path>
															<path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M39.743 44.461c2.555 -0.208 4.51 -2.163 4.718 -4.718C44.723 36.515 45 31.364 45 24c0 -7.364 -0.277 -12.515 -0.539 -15.743 -0.208 -2.555 -2.163 -4.51 -4.718 -4.718C36.515 3.277 31.364 3 24 3c-7.364 0 -12.515 0.277 -15.743 0.539 -2.555 0.208 -4.51 2.163 -4.718 4.718C3.277 11.485 3 16.636 3 24c0 7.364 0.277 12.515 0.539 15.743 0.208 2.555 2.163 4.51 4.718 4.718C11.485 44.723 16.636 45 24 45c7.364 0 12.515 -0.277 15.743 -0.539Z" stroke-width="3"></path>
															<path id="Vector 79 (Stroke)_2" fill="" fill-rule="evenodd" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M31.66 34.652a3.718 3.718 0 0 1 -4.974 0.524 180.782 180.782 0 0 1 -4.94 -3.73 0.98 0.98 0 0 0 -1.402 0.184c-1.127 1.552 -2.378 3.507 -3.385 5.137 -1.173 1.9 -3.487 2.876 -5.344 1.636a11.934 11.934 0 0 1 -1.324 -1.021c-1.576 -1.395 -1.414 -3.735 -0.046 -5.335 2.15 -2.517 4.727 -5.362 7.224 -7.79 1.459 -1.417 3.752 -1.366 5.274 -0.017 1.522 1.348 3.428 2.987 4.958 4.138a0.952 0.952 0 0 0 1.274 -0.116c1.72 -1.853 4.115 -4.704 5.618 -6.52 0.917 -1.107 2.286 -1.858 3.51 -1.104 0.29 0.18 0.572 0.387 0.824 0.61 0.947 0.838 0.778 2.193 0.143 3.286 -1.212 2.087 -3.635 5.874 -7.41 10.118Z" clip-rule="evenodd" stroke-width="3"></path>
															<path id="Vector 1016" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 11h10" stroke-width="3"></path><path id="Vector 1017" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 17h6" stroke-width="3"></path>
														</g>
													</svg>
													<span className="link-text ml-4">
														GSC Insights
													</span>
												</div>
												{/* <span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu">
												Upgrade ⚡
											</span> */}
											</NavLink>
											:
											<NavLink to={pageURL['GSCAnalytics']} className={({ isActive }) => isActive ? "is-active" : ""}
												onClick={(e) => {
													checkForUnsavedChanges(e);
													setHamburgerActive(false);
												}}>
												<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Content-Statistic--Streamline-Plump" height="48" width="48">
													<g id="content-statistic--product-data-analysis-analytics-graph-line-business-board-chart">
														<path id="Rectangle 1097" fill="" d="M39.743 44.461c2.555 -0.208 4.51 -2.163 4.718 -4.718C44.723 36.515 45 31.364 45 24c0 -7.364 -0.277 -12.515 -0.539 -15.743 -0.208 -2.555 -2.163 -4.51 -4.718 -4.718C36.515 3.277 31.364 3 24 3c-7.364 0 -12.515 0.277 -15.743 0.539 -2.555 0.208 -4.51 2.163 -4.718 4.718C3.277 11.485 3 16.636 3 24c0 7.364 0.277 12.515 0.539 15.743 0.208 2.555 2.163 4.51 4.718 4.718C11.485 44.723 16.636 45 24 45c7.364 0 12.515 -0.277 15.743 -0.539Z" stroke-width="3"></path>
														<path id="Vector 79 (Stroke)" fill="" fill-rule="evenodd" d="M31.659 34.651a3.718 3.718 0 0 1 -4.973 0.525 180.782 180.782 0 0 1 -4.941 -3.73 0.98 0.98 0 0 0 -1.402 0.184c-1.126 1.552 -2.378 3.507 -3.384 5.137 -1.174 1.9 -3.487 2.876 -5.345 1.636a12.006 12.006 0 0 1 -1.323 -1.021c-1.577 -1.395 -1.414 -3.735 -0.047 -5.335 2.15 -2.517 4.727 -5.362 7.225 -7.79 1.458 -1.417 3.752 -1.366 5.274 -0.017 1.521 1.348 3.428 2.987 4.957 4.138a0.952 0.952 0 0 0 1.274 -0.116c1.721 -1.853 4.116 -4.704 5.619 -6.52 0.916 -1.108 2.286 -1.858 3.51 -1.104 0.29 0.18 0.571 0.387 0.824 0.61 0.947 0.838 0.778 2.193 0.143 3.286 -1.212 2.087 -3.635 5.874 -7.411 10.117Z" clip-rule="evenodd" stroke-width="3"></path>
														<path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M39.743 44.461c2.555 -0.208 4.51 -2.163 4.718 -4.718C44.723 36.515 45 31.364 45 24c0 -7.364 -0.277 -12.515 -0.539 -15.743 -0.208 -2.555 -2.163 -4.51 -4.718 -4.718C36.515 3.277 31.364 3 24 3c-7.364 0 -12.515 0.277 -15.743 0.539 -2.555 0.208 -4.51 2.163 -4.718 4.718C3.277 11.485 3 16.636 3 24c0 7.364 0.277 12.515 0.539 15.743 0.208 2.555 2.163 4.51 4.718 4.718C11.485 44.723 16.636 45 24 45c7.364 0 12.515 -0.277 15.743 -0.539Z" stroke-width="3"></path>
														<path id="Vector 79 (Stroke)_2" fill="" fill-rule="evenodd" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M31.66 34.652a3.718 3.718 0 0 1 -4.974 0.524 180.782 180.782 0 0 1 -4.94 -3.73 0.98 0.98 0 0 0 -1.402 0.184c-1.127 1.552 -2.378 3.507 -3.385 5.137 -1.173 1.9 -3.487 2.876 -5.344 1.636a11.934 11.934 0 0 1 -1.324 -1.021c-1.576 -1.395 -1.414 -3.735 -0.046 -5.335 2.15 -2.517 4.727 -5.362 7.224 -7.79 1.459 -1.417 3.752 -1.366 5.274 -0.017 1.522 1.348 3.428 2.987 4.958 4.138a0.952 0.952 0 0 0 1.274 -0.116c1.72 -1.853 4.115 -4.704 5.618 -6.52 0.917 -1.107 2.286 -1.858 3.51 -1.104 0.29 0.18 0.572 0.387 0.824 0.61 0.947 0.838 0.778 2.193 0.143 3.286 -1.212 2.087 -3.635 5.874 -7.41 10.118Z" clip-rule="evenodd" stroke-width="3"></path>
														<path id="Vector 1016" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 11h10" stroke-width="3"></path><path id="Vector 1017" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 17h6" stroke-width="3"></path>
													</g>
												</svg>
												<span className="link-text ml-4">GSC Insights</span>
											</NavLink>
										}
									</li>
								)}


								{/* ------------------------- Reddit Post Finder ------------------------- */}
								<li className="mt-2 articles subcategory-menu" >
									<NavLink to={pageURL['redditPostFinder']} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" className="svg-reddit-icon" fill="none" viewBox="0 0 48 48" height="48" width="48">
											<g id="ai-edit-robot--change-edit-modify-pencil-write-writing-artificial-intelligence-ai">
												<path id="Rectangle 63" fill="" d="M44.277 24.287c1.652 -1.652 2.134 -4.136 0.765 -6.029a28.135 28.135 0 0 0 -2.907 -3.393 28.135 28.135 0 0 0 -3.393 -2.907c-1.893 -1.369 -4.377 -0.887 -6.029 0.765L12.186 33.25c-0.575 0.576 -0.955 1.316 -1.027 2.126 -0.136 1.546 -0.282 4.504 0.012 8.704a1.886 1.886 0 0 0 1.749 1.75c4.2 0.293 7.159 0.147 8.704 0.01 0.81 -0.07 1.55 -0.45 2.126 -1.026l20.527 -20.527Z" stroke-width="3"></path>
												<path id="Intersect" fill="" d="M23.43 45.1a4.171 4.171 0 0 0 -0.415 -1.673c-0.625 -1.258 -1.822 -3.167 -4.05 -5.394 -2.757 -2.757 -5.026 -3.936 -6.177 -4.404a3.57 3.57 0 0 0 -0.761 -0.21 3.406 3.406 0 0 0 -0.868 1.957c-0.136 1.545 -0.282 4.504 0.012 8.704a1.886 1.886 0 0 0 1.749 1.75c4.2 0.293 7.159 0.147 8.704 0.01a3.374 3.374 0 0 0 1.807 -0.74Z" stroke-width="3"></path>
												<path id="Rectangle 62" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M44.202 24.362c1.697 -1.697 2.193 -4.25 0.778 -6.19a28.204 28.204 0 0 0 -2.845 -3.307 28.215 28.215 0 0 0 -3.307 -2.845c-1.94 -1.415 -4.493 -0.92 -6.19 0.778L12.21 33.225c-0.592 0.592 -0.982 1.352 -1.055 2.185 -0.135 1.549 -0.276 4.48 0.012 8.625 0.066 0.963 0.834 1.73 1.797 1.797 4.146 0.288 7.076 0.147 8.625 0.012 0.833 -0.073 1.593 -0.463 2.184 -1.055l20.428 -20.427Z" stroke-width="3"></path>
												<path id="Rectangle 63_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M40.189 27.402s-0.607 -2.703 -4.248 -6.344c-3.64 -3.64 -6.343 -4.247 -6.343 -4.247" stroke-width="3"></path><path id="Rectangle 64" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M22.675 44.915s-0.607 -2.703 -4.247 -6.343c-3.641 -3.641 -6.344 -4.248 -6.344 -4.248" stroke-width="3"></path>
												<path id="Rectangle 1097" fill="" d="M19.829 17.336c-0.128 1.718 -1.396 3.114 -3.108 3.32C15.23 20.835 13.245 21 11 21s-4.229 -0.165 -5.721 -0.344c-1.712 -0.206 -2.98 -1.602 -3.108 -3.32C2.081 16.112 2 14.696 2 13.5c0 -4.198 3.375 -7.45 7.573 -7.493a142.258 142.258 0 0 1 2.854 0C16.625 6.049 20 9.302 20 13.5c0 1.195 -0.08 2.612 -0.171 3.836Z" stroke-width="3"></path><path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M19.829 17.336c-0.128 1.718 -1.396 3.114 -3.108 3.32C15.23 20.835 13.245 21 11 21s-4.229 -0.165 -5.721 -0.344c-1.712 -0.206 -2.98 -1.602 -3.108 -3.32C2.081 16.112 2 14.696 2 13.5v0c0 -4.198 3.375 -7.45 7.573 -7.493a142.258 142.258 0 0 1 2.854 0C16.625 6.049 20 9.302 20 13.5v0c0 1.195 -0.08 2.612 -0.171 3.836Z" stroke-width="3"></path>
												<path id="Vector 857" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m11 2 0 4" stroke-width="3"></path><path id="Vector 858" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m8 13 0 1" stroke-width="3"></path><path id="Vector 859" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m14 13 0 1" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Reddit SEO</span>
									</NavLink>
								</li>

								{/* ------------------------- Free BackLinks ------------------------- */}
								<li className="mt-2 articles subcategory-menu">
									<NavLink to={"/backlinks"} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
											<g>
												<path id="hover" fill="" d="M19.35785416666667 3.6646666666666667c-2.955020833333333 -2.954541666666667 -7.745250000000001 -2.954541666666667 -10.699791666666666 0l-1.141375 1.141375a1.9166666666666667 1.9166666666666667 0 0 0 0 2.7106458333333334l0.8557916666666667 0.8557916666666667a1.9166666666666667 1.9166666666666667 0 0 0 2.7106458333333334 0l1.141375 -1.1408958333333332a2.5218541666666665 2.5218541666666665 0 0 1 3.5664375 3.5664375l-1.141375 1.141375a1.9166666666666667 1.9166666666666667 0 0 0 0 2.7106458333333334l0.8562708333333333 0.8557916666666667a1.9166666666666667 1.9166666666666667 0 0 0 2.7106458333333334 0l1.141375 -1.141375c2.954541666666667 -2.954541666666667 2.954541666666667 -7.745250000000001 0 -10.699791666666666Z" strokeWidth="1"></path>
												<path id="hover" fill="" d="M3.6641875 19.357375c-2.954541666666667 -2.954541666666667 -2.954541666666667 -7.745250000000001 0 -10.699791666666666l1.141375 -1.141375a1.9166666666666667 1.9166666666666667 0 0 1 2.7106458333333334 0l0.8557916666666667 0.8562708333333333a1.9166666666666667 1.9166666666666667 0 0 1 0 2.7106458333333334l-1.141375 1.141375a2.5218541666666665 2.5218541666666665 0 0 0 3.5669166666666667 3.5664375l1.141375 -1.141375a1.9166666666666667 1.9166666666666667 0 0 1 2.7106458333333334 0l0.8557916666666667 0.8557916666666667a1.9166666666666667 1.9166666666666667 0 0 1 0 2.7106458333333334l-1.141375 1.141375c-2.954541666666667 2.954541666666667 -7.745250000000001 2.954541666666667 -10.699791666666666 0Z" strokeWidth="1"></path>
												<path id="Rectangle 1673" fill="" d="M7.230625 15.790458333333333a2.5218541666666665 2.5218541666666665 0 0 1 0 -3.5664375l4.993875 -4.992916666666667a2.5218541666666665 2.5218541666666665 0 0 1 3.5664375 3.5664375l-4.992916666666667 4.992916666666667a2.5218541666666665 2.5218541666666665 0 0 1 -3.5669166666666667 0Z" strokeWidth="1"></path><path id="Rectangle 1672" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M7.231104166666666 15.790458333333333a2.5218541666666665 2.5218541666666665 0 0 1 0 -3.5664375l4.992916666666667 -4.992916666666667a2.5218541666666665 2.5218541666666665 0 0 1 3.5669166666666667 3.5664375l-4.992916666666667 4.992916666666667a2.5218541666666665 2.5218541666666665 0 0 1 -3.5669166666666667 0Z" strokeWidth="1"></path>
												<path id="Union_3" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M3.6641875 19.357375c-2.954541666666667 -2.954541666666667 -2.954541666666667 -7.745250000000001 0 -10.699791666666666l1.141375 -1.141375a1.9166666666666667 1.9166666666666667 0 0 1 2.7106458333333334 0l0.8557916666666667 0.8562708333333333a1.9166666666666667 1.9166666666666667 0 0 1 0 2.7106458333333334l-1.141375 1.141375a2.5218541666666665 2.5218541666666665 0 0 0 3.5669166666666667 3.5664375l1.141375 -1.141375a1.9166666666666667 1.9166666666666667 0 0 1 2.7106458333333334 0l0.8557916666666667 0.8557916666666667a1.9166666666666667 1.9166666666666667 0 0 1 0 2.7106458333333334l-1.141375 1.141375c-2.954541666666667 2.954541666666667 -7.745250000000001 2.954541666666667 -10.699791666666666 0Z" strokeWidth="1"></path>
												<path id="Union_4" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M19.35785416666667 3.6646666666666667c-2.955020833333333 -2.954541666666667 -7.745250000000001 -2.954541666666667 -10.699791666666666 0l-1.141375 1.141375a1.9166666666666667 1.9166666666666667 0 0 0 0 2.7106458333333334l0.8557916666666667 0.8557916666666667a1.9166666666666667 1.9166666666666667 0 0 0 2.7106458333333334 0l1.141375 -1.1408958333333332a2.5218541666666665 2.5218541666666665 0 0 1 3.5664375 3.5664375l-1.141375 1.141375a1.9166666666666667 1.9166666666666667 0 0 0 0 2.7106458333333334l0.8562708333333333 0.8557916666666667a1.9166666666666667 1.9166666666666667 0 0 0 2.7106458333333334 0l1.141375 -1.141375c2.954541666666667 -2.954541666666667 2.954541666666667 -7.745250000000001 0 -10.699791666666666Z" strokeWidth="1">
												</path>
											</g>
										</svg>
										<span className="link-text ml-4">Backlinks Directory</span>
									</NavLink>
								</li>


								{/* ------------------------- GUEST POST FINDER ------------------------- */}
								<li className="mt-2 articles subcategory-menu">
									<NavLink to={pageURL['guestPostFinder']} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" className="svg-article-icon">
											<g>
												<path id="Vector" fill="" d="M24 3c6.074 0 9.586 0.256 11.573 0.51 1.618 0.205 2.926 1.179 3.811 2.549 1.148 1.774 2.802 4.493 4.145 7.338 1.1 2.332 1.47 4.918 1.47 7.497L45 30.5c0 3.865 -0.203 7.058 -0.425 9.4 -0.253 2.654 -2.398 4.619 -5.06 4.754 -3.32 0.168 -8.487 0.346 -15.515 0.346 -7.028 0 -12.195 -0.178 -15.515 -0.346 -2.662 -0.136 -4.808 -2.1 -5.06 -4.754A99.906 99.906 0 0 1 3 30.5l0 -9.606c0 -2.58 0.37 -5.165 1.471 -7.497 1.343 -2.845 2.997 -5.564 4.145 -7.338 0.885 -1.37 2.193 -2.344 3.81 -2.55C14.415 3.256 17.927 3 24 3Z" stroke-width="3"></path>
												<path id="Rectangle 1095" fill="" d="M3.425 39.974c0.245 2.586 2.323 4.485 4.916 4.633 3.314 0.19 8.53 0.393 15.659 0.393 7.13 0 12.345 -0.203 15.659 -0.393 2.593 -0.148 4.671 -2.047 4.916 -4.633 0.222 -2.355 0.425 -5.678 0.425 -9.974s-0.203 -7.619 -0.425 -9.974c-0.245 -2.586 -2.323 -4.485 -4.916 -4.633C36.345 15.203 31.129 15 24 15c-7.13 0 -12.345 0.203 -15.659 0.393 -2.593 0.148 -4.671 2.047 -4.916 4.633C3.203 22.381 3 25.704 3 30s0.203 7.619 0.425 9.974Z" stroke-width="3"></path>
												<path id="Vector_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M24 3c6.074 0 9.586 0.256 11.573 0.51 1.618 0.205 2.926 1.179 3.811 2.549 1.148 1.774 2.802 4.493 4.145 7.338 1.1 2.332 1.47 4.918 1.47 7.497L45 30.5c0 3.865 -0.203 7.058 -0.425 9.4 -0.253 2.654 -2.398 4.619 -5.06 4.754 -3.32 0.168 -8.487 0.346 -15.515 0.346 -7.028 0 -12.195 -0.178 -15.515 -0.346 -2.662 -0.136 -4.808 -2.1 -5.06 -4.754A99.906 99.906 0 0 1 3 30.5l0 -9.606c0 -2.58 0.37 -5.165 1.471 -7.497 1.343 -2.845 2.997 -5.564 4.145 -7.338 0.885 -1.37 2.193 -2.344 3.81 -2.55C14.415 3.256 17.927 3 24 3Z" stroke-width="3"></path>
												<path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3.425 39.974c0.245 2.586 2.323 4.485 4.916 4.633 3.314 0.19 8.53 0.393 15.659 0.393 7.13 0 12.345 -0.203 15.659 -0.393 2.593 -0.148 4.671 -2.047 4.916 -4.633 0.222 -2.355 0.425 -5.678 0.425 -9.974s-0.203 -7.619 -0.425 -9.974c-0.245 -2.586 -2.323 -4.485 -4.916 -4.633C36.345 15.203 31.129 15 24 15c-7.13 0 -12.345 0.203 -15.659 0.393 -2.593 0.148 -4.671 2.047 -4.916 4.633C3.203 22.381 3 25.704 3 30s0.203 7.619 0.425 9.974Z" stroke-width="3"></path><path id="Vector 844" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M24 3v12" stroke-width="3"></path>
												<path id="Union" fill="" d="M16.744 28.676c-0.971 1.24 -0.296 2.726 1.277 2.818a78.86 78.86 0 0 0 2.59 0.104c0.074 1.83 0.169 3.67 0.238 4.928 0.053 0.98 0.685 1.838 1.655 1.99 0.437 0.067 0.945 0.117 1.497 0.117 0.552 0 1.06 -0.05 1.498 -0.118 0.971 -0.15 1.604 -1.01 1.657 -1.991 0.068 -1.259 0.162 -3.096 0.235 -4.926a77.622 77.622 0 0 0 2.588 -0.104c1.574 -0.092 2.25 -1.578 1.278 -2.818a62.244 62.244 0 0 0 -2.347 -2.804c-1.539 -1.743 -2.716 -2.783 -3.532 -3.396 -0.84 -0.632 -1.914 -0.632 -2.755 0 -0.815 0.613 -1.992 1.654 -3.531 3.396a62.205 62.205 0 0 0 -2.348 2.804Z" stroke-width="3"></path>
												<path id="Union_2" fill="" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M16.744 28.676c-0.971 1.24 -0.296 2.726 1.277 2.818a78.86 78.86 0 0 0 2.59 0.104c0.074 1.83 0.169 3.67 0.238 4.928 0.053 0.98 0.685 1.838 1.655 1.99 0.437 0.067 0.945 0.117 1.497 0.117 0.552 0 1.06 -0.05 1.498 -0.118 0.971 -0.15 1.604 -1.01 1.657 -1.991 0.068 -1.259 0.162 -3.096 0.235 -4.926a77.622 77.622 0 0 0 2.588 -0.104c1.574 -0.092 2.25 -1.578 1.278 -2.818a62.244 62.244 0 0 0 -2.347 -2.804c-1.539 -1.743 -2.716 -2.783 -3.532 -3.396 -0.84 -0.632 -1.914 -0.632 -2.755 0 -0.815 0.613 -1.992 1.654 -3.531 3.396a62.205 62.205 0 0 0 -2.348 2.804Z" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Guest Post Finder</span>
									</NavLink>

								</li>

								{/* -------------------------  Fast Indexing  ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink
										to={isProduction ? "" : pageURL['indexation']}
										className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Fingerprint-2--Streamline-Plump" height="48" width="48">
												<g id="fingerprint-2--identification-password-touch-id-secure-fingerprint-finger-security">
													<path id="Vector 1073" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m24 20 0 8" stroke-width="3"></path>
													<path id="Rectangle 1095" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M15 20.75c0 -0.178 0 -0.356 0.002 -0.533 0.03 -5.062 3.925 -9.217 8.987 -9.217 5.068 0 8.889 4.15 8.978 9.216a213.473 213.473 0 0 1 0 7.568C32.877 32.851 29.057 37 23.99 37c-5.073 0 -8.961 -4.144 -8.988 -9.217L15 27.25" stroke-width="3"></path>
													<path id="Rectangle 1643" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M33.5 5.708A17.916 17.916 0 0 0 24 3C14.059 3 6 11.059 6 21v6c0 8.743 6.234 16.031 14.5 17.66" stroke-width="3"></path>
													<path id="Rectangle 1644" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M38.622 10.5A17.918 17.918 0 0 1 42 21v6c0 8.743 -6.234 16.031 -14.5 17.66" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">Fast Indexing</span>
										</div>
										{isProduction && (
											<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu">
												Coming Soon
											</span>
										)}
									</NavLink>
								</li>

								{/* ------------------------- AI Calculator Generator ------------------------- */}
								{!isProduction ? (
									<li className="mt-2 articles subcategory-menu" >
										<NavLink to={pageURL['aiCalculatorGenerator']} className={({ isActive }) => isActive ? "is-active" : ""}
											onClick={(e) => {
												checkForUnsavedChanges(e);
												setHamburgerActive(false);
											}}>
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Calculator-1--Streamline-Plump" height="48" width="48">
												<g id="calculator-1--shop-shopping-pay-payment-store-cash-calculate-math">
													<path id="Rectangle 1097" fill="" d="M3.53896 8.25718c0.20761 -2.5553 2.16291 -4.5106 4.71821 -4.71821C11.4854 3.27668 16.636 3 24 3c7.364 0 12.5146 0.27667 15.7428 0.53896 2.5553 0.20761 4.5106 2.16291 4.7182 4.71821C44.7233 11.4854 45 16.636 45 24c0 7.364 -0.2767 12.5146 -0.539 15.7428 -0.2076 2.5553 -2.1629 4.5106 -4.7182 4.7182C36.5146 44.7233 31.364 45 24 45c-7.364 0 -12.5146 -0.2767 -15.74282 -0.539 -2.5553 -0.2076 -4.5106 -2.1629 -4.71821 -4.7182C3.27668 36.5146 3 31.364 3 24c0 -7.364 0.27667 -12.5146 0.53896 -15.74282Z" stroke-width="3"></path>
													<path id="Intersect" fill="" d="M9.06696 17.9705c-0.02621 1.1158 0.87498 2.0296 1.99104 2.0296h25.884c1.1161 0 2.0173 -0.9138 1.9911 -2.0296 -0.063 -2.6798 -0.1602 -4.6917 -0.2466 -6.0807 -0.0871 -1.4004 -1.1759 -2.48914 -2.5763 -2.57625C33.8255 9.17145 29.8557 9 24 9c-5.8557 0 -9.8255 0.17145 -12.1102 0.31355 -1.4004 0.08711 -2.48913 1.17585 -2.57624 2.57625 -0.08639 1.389 -0.18363 3.4009 -0.2466 6.0807Z" stroke-width="3"></path><path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3.53896 8.25718c0.20761 -2.5553 2.16291 -4.5106 4.71821 -4.71821C11.4854 3.27668 16.636 3 24 3c7.364 0 12.5146 0.27667 15.7428 0.53896 2.5553 0.20761 4.5106 2.16291 4.7182 4.71821C44.7233 11.4854 45 16.636 45 24c0 7.364 -0.2767 12.5146 -0.539 15.7428 -0.2076 2.5553 -2.1629 4.5106 -4.7182 4.7182C36.5146 44.7233 31.364 45 24 45c-7.364 0 -12.5146 -0.2767 -15.74282 -0.539 -2.5553 -0.2076 -4.5106 -2.1629 -4.71821 -4.7182C3.27668 36.5146 3 31.364 3 24c0 -7.364 0.27667 -12.5146 0.53896 -15.74282Z" stroke-width="3"></path>
													<path id="Intersect_2" fill="" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M9.06599 17.9705c-0.02622 1.1158 0.87498 2.0296 1.99101 2.0296h25.884c1.1161 0 2.0173 -0.9138 1.9911 -2.0296 -0.063 -2.6798 -0.1602 -4.6917 -0.2466 -6.0807 -0.0871 -1.4004 -1.1759 -2.48914 -2.5763 -2.57625C33.8246 9.17145 29.8548 9 23.999 9c-5.8557 0 -9.8255 0.17145 -12.1102 0.31355 -1.4004 0.08711 -2.48911 1.17585 -2.57621 2.57625 -0.0864 1.389 -0.18364 3.4009 -0.2466 6.0807Z" stroke-width="3"></path><path id="Vector 1422" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 28h4" stroke-width="3"></path>
													<path id="Vector 1425" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 37h4" stroke-width="3"></path>
													<path id="Vector 1423" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M22 28h4" stroke-width="3"></path>
													<path id="Vector 1426" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M22 37h4" stroke-width="3"></path>
													<path id="Vector 1424" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M33 28h4" stroke-width="3"></path>
													<path id="Vector 1427" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M33 37h4" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Calculator Generator</span>
										</NavLink>
									</li>

								) : (

									<li className="mt-2 autoArticles subcategory-menu pricing-plan">
										<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
											onClick={(e) => {
												e.preventDefault();
												checkForUnsavedChanges(e);
												setHamburgerActive(false);
											}}>
											<div className="category">
												<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Calculator-1--Streamline-Plump" height="48" width="48">
													<g id="calculator-1--shop-shopping-pay-payment-store-cash-calculate-math">
														<path id="Rectangle 1097" fill="" d="M3.53896 8.25718c0.20761 -2.5553 2.16291 -4.5106 4.71821 -4.71821C11.4854 3.27668 16.636 3 24 3c7.364 0 12.5146 0.27667 15.7428 0.53896 2.5553 0.20761 4.5106 2.16291 4.7182 4.71821C44.7233 11.4854 45 16.636 45 24c0 7.364 -0.2767 12.5146 -0.539 15.7428 -0.2076 2.5553 -2.1629 4.5106 -4.7182 4.7182C36.5146 44.7233 31.364 45 24 45c-7.364 0 -12.5146 -0.2767 -15.74282 -0.539 -2.5553 -0.2076 -4.5106 -2.1629 -4.71821 -4.7182C3.27668 36.5146 3 31.364 3 24c0 -7.364 0.27667 -12.5146 0.53896 -15.74282Z" stroke-width="3"></path>
														<path id="Intersect" fill="" d="M9.06696 17.9705c-0.02621 1.1158 0.87498 2.0296 1.99104 2.0296h25.884c1.1161 0 2.0173 -0.9138 1.9911 -2.0296 -0.063 -2.6798 -0.1602 -4.6917 -0.2466 -6.0807 -0.0871 -1.4004 -1.1759 -2.48914 -2.5763 -2.57625C33.8255 9.17145 29.8557 9 24 9c-5.8557 0 -9.8255 0.17145 -12.1102 0.31355 -1.4004 0.08711 -2.48913 1.17585 -2.57624 2.57625 -0.08639 1.389 -0.18363 3.4009 -0.2466 6.0807Z" stroke-width="3"></path><path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3.53896 8.25718c0.20761 -2.5553 2.16291 -4.5106 4.71821 -4.71821C11.4854 3.27668 16.636 3 24 3c7.364 0 12.5146 0.27667 15.7428 0.53896 2.5553 0.20761 4.5106 2.16291 4.7182 4.71821C44.7233 11.4854 45 16.636 45 24c0 7.364 -0.2767 12.5146 -0.539 15.7428 -0.2076 2.5553 -2.1629 4.5106 -4.7182 4.7182C36.5146 44.7233 31.364 45 24 45c-7.364 0 -12.5146 -0.2767 -15.74282 -0.539 -2.5553 -0.2076 -4.5106 -2.1629 -4.71821 -4.7182C3.27668 36.5146 3 31.364 3 24c0 -7.364 0.27667 -12.5146 0.53896 -15.74282Z" stroke-width="3"></path>
														<path id="Intersect_2" fill="" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M9.06599 17.9705c-0.02622 1.1158 0.87498 2.0296 1.99101 2.0296h25.884c1.1161 0 2.0173 -0.9138 1.9911 -2.0296 -0.063 -2.6798 -0.1602 -4.6917 -0.2466 -6.0807 -0.0871 -1.4004 -1.1759 -2.48914 -2.5763 -2.57625C33.8246 9.17145 29.8548 9 23.999 9c-5.8557 0 -9.8255 0.17145 -12.1102 0.31355 -1.4004 0.08711 -2.48911 1.17585 -2.57621 2.57625 -0.0864 1.389 -0.18364 3.4009 -0.2466 6.0807Z" stroke-width="3"></path><path id="Vector 1422" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 28h4" stroke-width="3"></path>
														<path id="Vector 1425" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M11 37h4" stroke-width="3"></path>
														<path id="Vector 1423" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M22 28h4" stroke-width="3"></path>
														<path id="Vector 1426" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M22 37h4" stroke-width="3"></path>
														<path id="Vector 1424" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M33 28h4" stroke-width="3"></path>
														<path id="Vector 1427" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M33 37h4" stroke-width="3"></path>
													</g>
												</svg>
												<span className="link-text ml-4">AI Calculator Generator</span>
											</div>
											<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
												Coming Soon
											</span>
										</NavLink>
									</li>

								)}

								{/* -------------------------  AI Auto Schema ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu">
									<NavLink to={isProduction ? "" : pageURL["aiAutoSchema"]} className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}
										}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2" height="24" width="24">
												<path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
											</svg>
											<span className="link-text ml-4">AI Auto Schema</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* ------------------------- Website Scanning ------------------------- */}
								{/* <li className="mt-2 articles subcategory-menu">
								<NavLink to={pageURL['websiteScanning']}
									className={({ isActive }) => isActive ? "is-active" : ""}
									onClick={(e) => {
										checkForUnsavedChanges(e);
										setHamburgerActive(false);
									}}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
										<g>
											<path id="Rectangle 65" fill="" d="M42.248 44.962c1.438 -0.055 2.534 -0.986 2.657 -2.42A29.99 29.99 0 0 0 45 40c0 -1.05 -0.04 -1.884 -0.096 -2.542 -0.122 -1.434 -1.218 -2.365 -2.656 -2.42A59.122 59.122 0 0 0 40 35c-0.914 0 -1.653 0.015 -2.248 0.038 -1.438 0.055 -2.534 0.986 -2.657 2.42A29.99 29.99 0 0 0 35 40c0 1.05 0.04 1.884 0.096 2.542 0.122 1.434 1.218 2.365 2.656 2.42A58.69 58.69 0 0 0 40 45c0.914 0 1.653 -0.015 2.248 -0.038Z" strokeWidth="3"></path>
											<path id="hover" fill="" d="M42.248 28.962c1.438 -0.055 2.534 -0.986 2.657 -2.42A29.99 29.99 0 0 0 45 24c0 -1.05 -0.04 -1.884 -0.096 -2.542 -0.122 -1.434 -1.218 -2.365 -2.656 -2.42A59.122 59.122 0 0 0 40 19c-0.914 0 -1.653 0.015 -2.248 0.038 -1.438 0.055 -2.534 0.986 -2.657 2.42A29.99 29.99 0 0 0 35 24c0 1.05 0.04 1.884 0.096 2.542 0.122 1.434 1.218 2.365 2.656 2.42A58.69 58.69 0 0 0 40 29c0.914 0 1.653 -0.015 2.248 -0.038Z" strokeWidth="3"></path>
											<path id="Rectangle 63" fill="" d="M42.248 12.962c1.438 -0.055 2.534 -0.986 2.657 -2.42A29.99 29.99 0 0 0 45 8c0 -1.05 -0.04 -1.884 -0.096 -2.542 -0.122 -1.434 -1.218 -2.365 -2.656 -2.42A59.155 59.155 0 0 0 40 3c-0.914 0 -1.653 0.015 -2.248 0.038 -1.438 0.055 -2.534 0.986 -2.657 2.42A29.988 29.988 0 0 0 35 8c0 1.05 0.04 1.884 0.096 2.542 0.122 1.434 1.218 2.365 2.656 2.42A58.69 58.69 0 0 0 40 13c0.914 0 1.653 -0.015 2.248 -0.038Z" strokeWidth="3"></path>
											<path id="hover" fill="" d="M19.305 33.831c1.723 -0.114 3.06 -1.236 3.303 -2.945 0.21 -1.475 0.392 -3.68 0.392 -6.886s-0.183 -5.411 -0.392 -6.886c-0.242 -1.709 -1.58 -2.83 -3.303 -2.945C17.923 14.077 15.901 14 13 14c-2.9 0 -4.923 0.077 -6.305 0.169 -1.723 0.114 -3.06 1.236 -3.303 2.945C3.182 18.59 3 20.794 3 24s0.183 5.411 0.392 6.886c0.243 1.709 1.58 2.83 3.303 2.945C8.077 33.923 10.099 34 13 34c2.9 0 4.923 -0.077 6.305 -0.169Z" strokeWidth="3"></path>
											<path id="Vector 978" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m23 24 12 0" strokeWidth="3"></path>
											<path id="Rectangle 56" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M19.305 33.831c1.723 -0.114 3.06 -1.236 3.303 -2.945 0.21 -1.475 0.392 -3.68 0.392 -6.886s-0.183 -5.411 -0.392 -6.886c-0.242 -1.709 -1.58 -2.83 -3.303 -2.945C17.923 14.077 15.901 14 13 14c-2.9 0 -4.923 0.077 -6.305 0.169 -1.723 0.114 -3.06 1.236 -3.303 2.945C3.182 18.59 3 20.794 3 24s0.183 5.411 0.392 6.886c0.243 1.709 1.58 2.83 3.303 2.945C8.077 33.923 10.099 34 13 34c2.9 0 4.923 -0.077 6.305 -0.169Z" strokeWidth="3"></path>
											<path id="Rectangle 58" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M42.248 12.962c1.438 -0.055 2.534 -0.986 2.657 -2.42A29.99 29.99 0 0 0 45 8c0 -1.05 -0.04 -1.884 -0.096 -2.542 -0.122 -1.434 -1.218 -2.365 -2.656 -2.42A59.155 59.155 0 0 0 40 3c-0.914 0 -1.653 0.015 -2.248 0.038 -1.438 0.055 -2.534 0.986 -2.657 2.42A29.988 29.988 0 0 0 35 8c0 1.05 0.04 1.884 0.096 2.542 0.122 1.434 1.218 2.365 2.656 2.42A58.69 58.69 0 0 0 40 13c0.914 0 1.653 -0.015 2.248 -0.038Z" strokeWidth="3"></path>
											<path id="Rectangle 60" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M42.248 28.962c1.438 -0.055 2.534 -0.986 2.657 -2.42A29.99 29.99 0 0 0 45 24c0 -1.05 -0.04 -1.884 -0.096 -2.542 -0.122 -1.434 -1.218 -2.365 -2.656 -2.42A59.122 59.122 0 0 0 40 19c-0.914 0 -1.653 0.015 -2.248 0.038 -1.438 0.055 -2.534 0.986 -2.657 2.42A29.99 29.99 0 0 0 35 24c0 1.05 0.04 1.884 0.096 2.542 0.122 1.434 1.218 2.365 2.656 2.42A58.69 58.69 0 0 0 40 29c0.914 0 1.653 -0.015 2.248 -0.038Z" strokeWidth="3"></path>
											<path id="Rectangle 61" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M42.248 44.962c1.438 -0.055 2.534 -0.986 2.657 -2.42A29.99 29.99 0 0 0 45 40c0 -1.05 -0.04 -1.884 -0.096 -2.542 -0.122 -1.434 -1.218 -2.365 -2.656 -2.42A59.122 59.122 0 0 0 40 35c-0.914 0 -1.653 0.015 -2.248 0.038 -1.438 0.055 -2.534 0.986 -2.657 2.42A29.99 29.99 0 0 0 35 40c0 1.05 0.04 1.884 0.096 2.542 0.122 1.434 1.218 2.365 2.656 2.42A58.69 58.69 0 0 0 40 45c0.914 0 1.653 -0.015 2.248 -0.038Z" strokeWidth="3"></path>
											<path id="Subtract" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m35.002 39.546 -2.364 -0.215A4 4 0 0 1 29 35.347l0 -22.694a4 4 0 0 1 3.638 -3.984l2.364 -0.214" strokeWidth="3"></path>
										</g>
									</svg>
									<span className="link-text ml-4">Internal Links</span>
								</NavLink>
							</li> */}

								{/* ------------------------- GLOSSARY GENERATOR  ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu" ref={glossaryDropdownRef}>
									<NavLink to={isProduction ? "" : pageURL['glossary']} className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											toggleGlossaryGenerator();
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Ascending-Alphabetical-Order--Streamline-Plump" height="48" width="48">
												<g id="ascending-alphabetical-order">
													<path id="Union" fill="" d="M39.891 36.405c1.591 0.036 2.772 0.094 3.637 0.156 1.049 0.074 1.506 1.164 0.849 1.985 -2.976 3.719 -5.768 5.755 -7.126 6.617a1.587 1.587 0 0 1 -1.72 0c-1.358 -0.862 -4.15 -2.898 -7.126 -6.617 -0.657 -0.82 -0.2 -1.911 0.849 -1.985 0.865 -0.062 2.046 -0.12 3.637 -0.156V20c0 -8.523 0.152 -13.007 0.255 -15.076 0.048 -0.945 0.612 -1.734 1.551 -1.84 0.439 -0.05 0.995 -0.084 1.694 -0.084s1.255 0.035 1.694 0.084c0.94 0.106 1.504 0.895 1.55 1.84 0.105 2.07 0.256 6.553 0.256 15.076v16.405Z" stroke-width="3"></path>
													<path id="Vector" fill="" d="M16 17.155h-5.37c-0.209 0.7 -0.419 1.295 -0.624 1.902 -0.32 0.946 -1.041 1.75 -2.032 1.87 -1.13 0.139 -2.347 0.047 -3.204 -0.497 -0.777 -0.544 -0.91 -1.472 -0.648 -2.326 1.133 -3.7 3.428 -9.955 4.631 -13.187 0.499 -1.338 1.461 -2.518 2.868 -2.764A10.694 10.694 0 0 1 13.448 2c1.46 0 3.87 0.22 4.468 1.843 1.792 4.737 3.198 9.64 4.61 14.509 0.294 1.009 0 2.114 -1.014 2.388 -1.163 0.313 -2.657 0.351 -3.681 0.071 -0.58 -0.158 -0.929 -0.686 -1.108 -1.258a94.007 94.007 0 0 1 -0.724 -2.398ZM13.511 8a0.127 0.127 0 0 0 -0.122 0.093l-0.946 3.407h2.112l-0.92 -3.406A0.127 0.127 0 0 0 13.511 8Z" stroke-width="3"></path>
													<path id="Vector_2" fill="" d="M7.479 46c-1.33 0 -2.825 -0.36 -3.284 -1.608 -0.292 -0.796 -0.207 -1.564 -0.063 -2.107 0.141 -0.535 0.491 -0.973 0.879 -1.367 2.197 -2.236 8.453 -8.693 8.453 -8.693H6.776c-0.898 0 -1.799 -0.383 -2.145 -1.211a4.444 4.444 0 0 1 -0.357 -1.705c0 -1.468 0.98 -2.309 2.422 -2.309h12.56c1.163 0 2.434 0.25 3.05 1.236 0.646 1.032 0.465 2.079 0.242 2.713 -0.148 0.42 -0.434 0.76 -0.744 1.079 -2.15 2.202 -8.34 8.747 -8.34 8.747h7.044c0.966 0 1.907 0.46 2.21 1.378 0.175 0.533 0.282 1.092 0.282 1.57C23 45.257 22.148 46 20.616 46H7.479Z" stroke-width="3"></path>
													<path id="Vector_3" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M7.479 46c-1.33 0 -2.825 -0.36 -3.284 -1.608 -0.292 -0.796 -0.207 -1.564 -0.063 -2.107 0.141 -0.535 0.491 -0.973 0.879 -1.367 2.197 -2.236 8.453 -8.693 8.453 -8.693H6.776c-0.898 0 -1.799 -0.383 -2.145 -1.211a4.444 4.444 0 0 1 -0.357 -1.705c0 -1.468 0.98 -2.309 2.422 -2.309h12.56c1.163 0 2.434 0.25 3.05 1.236 0.646 1.032 0.465 2.079 0.242 2.713 -0.148 0.42 -0.434 0.76 -0.744 1.079 -2.15 2.202 -8.34 8.747 -8.34 8.747h7.044c0.966 0 1.907 0.46 2.21 1.378 0.175 0.533 0.282 1.092 0.282 1.57C23 45.257 22.148 46 20.616 46H7.479Z" stroke-width="3"></path>
													<path id="Vector_4" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M16 17.155h-5.37c-0.209 0.7 -0.419 1.295 -0.624 1.902 -0.32 0.946 -1.041 1.75 -2.032 1.87 -1.13 0.139 -2.347 0.047 -3.204 -0.497 -0.777 -0.544 -0.91 -1.472 -0.648 -2.326 1.133 -3.7 3.428 -9.955 4.631 -13.187 0.499 -1.338 1.461 -2.518 2.868 -2.764A10.694 10.694 0 0 1 13.448 2c1.46 0 3.87 0.22 4.468 1.843 1.792 4.737 3.198 9.64 4.61 14.509 0.294 1.009 0 2.114 -1.014 2.388 -1.163 0.313 -2.657 0.351 -3.681 0.071 -0.58 -0.158 -0.929 -0.686 -1.108 -1.258a94.007 94.007 0 0 1 -0.724 -2.398ZM13.511 8a0.127 0.127 0 0 0 -0.122 0.093l-0.946 3.407h2.112l-0.92 -3.406A0.127 0.127 0 0 0 13.511 8Z" stroke-width="3"></path>
													<path id="Union_2" fill="" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M39.891 36.405c1.591 0.036 2.772 0.094 3.637 0.156 1.049 0.074 1.506 1.164 0.849 1.985 -2.976 3.719 -5.768 5.755 -7.126 6.617a1.587 1.587 0 0 1 -1.72 0c-1.358 -0.862 -4.15 -2.898 -7.126 -6.617 -0.657 -0.82 -0.2 -1.911 0.849 -1.985 0.865 -0.062 2.046 -0.12 3.637 -0.156V20c0 -8.523 0.152 -13.007 0.255 -15.076 0.048 -0.945 0.612 -1.734 1.551 -1.84 0.439 -0.05 0.995 -0.084 1.694 -0.084s1.255 0.035 1.694 0.084c0.94 0.106 1.504 0.895 1.55 1.84 0.105 2.07 0.256 6.553 0.256 15.076v16.405Z" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4"> AI Glossary Creator</span>
										</div>
										{/* <span className="drop-down-arrow"> */}
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
										{/* <svg xmlns="http://www.w3.org/2000/svg" className="arrow-svg" width="24" height="24" viewBox="0 0 24 24" fill="" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true" 
					                            style={{
                                                        transform: isGlossaryDropdownOpen ? 'rotate(90deg)' : 'rotate(0deg)',
                                                    }}>
                                                <path d="M9 18l6-6-6-6"  stroke="black"   fill="" />
                                            </svg> */}
										{/* </span> */}
									</NavLink>

									{/* {isGlossaryDropdownOpen ?
										<ul className="dropdown-menu-container" >
											------------------------- CREATE GUEST POST FINDER ------------------------- 
											<li className="articles subcategory-menu">
												<NavLink to={pageURL['glossary']} className={({ isActive }) => isActive ? "is-active" : ""}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">Create AI Glossary </span>
												</NavLink>
											</li>

											------------------------- GUEST POST FINDER PROJECTS -------------------------
											<li className="articles subcategory-menu">
												<NavLink to={pageURL['glossaryHome']}
													className={({ isActive }) => isActive ? "is-active" : ""}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">AI Glossary Projects</span>
												</NavLink>
											</li>
										</ul>
										: ""} */}
								</li>

								{/* -------------------------  AI Article Updater ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											e.preventDefault();
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-10 -10 320 320" id="Hand-Held--Streamline-Plump" height="320" width="320">
												<g id="hand-held--tablet-kindle-device-electronics-ipad-computer">
													<path id="Rectangle 1095" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M34.29875 248.406875c1.1734375 15.958749999999998 13.2079375 28.3275 29.151875 29.691250000000004C81.82000000000001 279.669375 110.19000000000001 281.25 150 281.25c39.809375 0 68.17999999999999 -1.5806250000000002 86.549375 -3.151875 15.943750000000001 -1.36375 27.978125 -13.7325 29.151875 -29.691250000000004C267.185 228.23125000000002 268.75 196.035625 268.75 150s-1.5650000000000002 -78.23125 -3.04875 -98.40675c-1.17375 -15.959000000000001 -13.208125 -28.3275625 -29.151875 -29.6914375C218.18 20.330437500000002 189.81 18.75 150 18.75c-39.809375 0 -68.17999999999999 1.5804375 -86.549375 3.1518125 -15.943874999999998 1.363875 -27.9784375 13.7324375 -29.151875 29.6914375C32.81525 71.76875 31.25 103.964375 31.25 150s1.56525 78.23125 3.04875 98.406875Z" stroke-width="20"></path>
													<path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M71.10625 201.593125c0.711875 7.9799999999999995 6.820625 14.08875 14.799999999999999 14.800625C98.073125 217.47875 118.951875 218.75 150 218.75c31.048125 0 51.926874999999995 -1.27125 64.093125 -2.35625 7.9799999999999995 -0.711875 14.08875 -6.820625 14.800625 -14.799999999999999C229.97875 189.426875 231.25 168.54875 231.25 137.5c0 -31.048125 -1.27125 -51.926874999999995 -2.35625 -64.09375 -0.711875 -7.979374999999999 -6.820625 -14.088187499999998 -14.800625 -14.799937499999999C201.92687500000002 57.52125 181.048125 56.25 150 56.25c-31.048125 0 -51.926874999999995 1.2711875 -64.093125 2.3563125 -7.9799999999999995 0.71175 -14.08875 6.8205625 -14.800625 14.799937499999999C70.02125 85.573125 68.75 106.451875 68.75 137.5c0 31.048125 1.27125 51.926874999999995 2.35625 64.093125Z" stroke-width="20"></path>
													<path id="Vector 1491" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M143.75 250h12.5" stroke-width="20"></path>
													<path id="Vector 1495" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M112.5 100h75" stroke-width="20"></path>
													<path id="Vector 1496" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M112.5 137.5h75" stroke-width="20"></path>
													<path id="Vector 1497" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M112.5 175h37.5" stroke-width="20"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Article Updater</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  AI Comparison Pages ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-10 -10 320 320" id="Arrow-Cursor-Move--Streamline-Plump" height="320" width="320">
												<g id="arrow-cursor-move--mouse-select-cursor-move-scroll">
													<path id="Rectangle 128" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M140.50625000000002 78.39375c13.075000000000001 -4.4624999999999995 15.225 -18.993750000000002 2.675 -24.76875 -28.625 -13.175 -73.9875 -28.0625 -110.1125 -33.29375a11.15625 11.15625 0 0 0 -12.737499999999999 12.737499999999999c5.2375 36.125 20.125 81.48125 33.29375 110.1125 5.775 12.55 20.306250000000002 10.4 24.76875 -2.675l15.812499999999998 -46.300000000000004 46.300000000000004 -15.812499999999998Z" stroke-width="20"></path>
													<path id="Rectangle 129" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M212.92499999999998 136.29375c3.3125 -0.43125 4.8625 -3.58125 3.4062500000000004 -6.575 -2.075 -4.2625 -5.7125 -11.1375 -11.7875 -21.04375 -4.4375 -7.23125 -8 -12.043750000000001 -10.70625 -15.225 -3.5937499999999996 -4.21875 -9.0875 -4.21875 -12.675 0 -2.7 3.18125 -6.275 7.9937499999999995 -10.70625 15.225 -6.075 9.90625 -9.7125 16.78125 -11.7875 21.0375 -1.45625 3 0.1 6.15625 3.4062500000000004 6.58125 4.5875 0.6 12.4375 1.20625 25.424999999999997 1.20625 12.98125 0 20.837500000000002 -0.6062500000000001 25.424999999999997 -1.20625Z" stroke-width="20"></path>
													<path id="Rectangle 130" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M212.92499999999998 238.70624999999998c3.3125 0.43125 4.8625 3.58125 3.4062500000000004 6.575 -2.075 4.2625 -5.7125 11.1375 -11.7875 21.04375 -4.4375 7.23125 -8 12.043750000000001 -10.70625 15.225 -3.5937499999999996 4.21875 -9.0875 4.21875 -12.675 0 -2.7 -3.18125 -6.275 -7.9937499999999995 -10.70625 -15.225 -6.075 -9.90625 -9.7125 -16.78125 -11.7875 -21.04375 -1.45625 -2.99375 0.1 -6.14375 3.4062500000000004 -6.575 4.5875 -0.6 12.4375 -1.20625 25.424999999999997 -1.20625 12.98125 0 20.837500000000002 0.6062500000000001 25.424999999999997 1.20625Z" stroke-width="20"></path>
													<path id="Vector 2599" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m187.5 137.5 0 100" stroke-width="20"></path>
													<path id="Rectangle 129_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M238.70624999999998 212.93125c0.43125 3.3000000000000003 3.58125 4.85625 6.575 3.4000000000000004 4.2625 -2.06875 11.1375 -5.7125 21.04375 -11.7875 7.23125 -4.4375 12.043750000000001 -8 15.225 -10.70625 4.21875 -3.5875 4.21875 -9.08125 0 -12.668750000000001 -3.18125 -2.70625 -7.9937499999999995 -6.275 -15.225 -10.7125 -9.90625 -6.075 -16.78125 -9.7125 -21.04375 -11.78125 -2.99375 -1.4625000000000001 -6.14375 0.09375 -6.575 3.4000000000000004 -0.6 4.5875 -1.20625 12.4375 -1.20625 25.424999999999997 0 12.987499999999999 0.6062500000000001 20.837500000000002 1.20625 25.43125Z" stroke-width="20"></path>
													<path id="Rectangle 130_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M136.29375 212.93125c-0.43125 3.3000000000000003 -3.58125 4.85625 -6.575 3.4000000000000004 -4.2625 -2.06875 -11.1375 -5.7125 -21.04375 -11.7875 -7.23125 -4.4375 -12.043750000000001 -8 -15.225 -10.70625 -4.21875 -3.5875 -4.21875 -9.08125 0 -12.668750000000001 3.18125 -2.70625 7.9937499999999995 -6.275 15.225 -10.7125 9.90625 -6.075 16.78125 -9.7125 21.0375 -11.78125 3 -1.4625000000000001 6.15625 0.09375 6.58125 3.4000000000000004 0.6 4.5875 1.20625 12.4375 1.20625 25.424999999999997 0 12.987499999999999 -0.6062500000000001 20.837500000000002 -1.20625 25.43125Z" stroke-width="20"></path>
													<path id="Vector 2599_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M237.5 187.5H137.5" stroke-width="20"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Comparison Pages</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  AI Statistic Page Generator ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-10 -10 320 320" id="Graph-Dot--Streamline-Plump" height="320" width="320">
												<g id="graph-dot--product-data-bars-analysis-analytics-graph-business-chart-dot">
													<path id="Ellipse 22" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M218.75 50a31.25 31.25 0 1 0 62.5 0 31.25 31.25 0 0 0 -62.5 0Z" stroke-width="20"></path>
													<path id="Ellipse 23" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M137.5 100a31.25 31.25 0 1 1 -62.5 0 31.25 31.25 0 1 1 62.5 0" stroke-width="20"></path>
													<path id="Ellipse 24" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M237.5 175a31.25 31.25 0 1 1 -62.5 0 31.25 31.25 0 1 1 62.5 0" stroke-width="20"></path>
													<path id="Vector 2496" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M21.875 218.75c18.175 -34.4375 45.725 -71.21875 64.35625 -94.5625" stroke-width="20"></path>
													<path id="Vector 2498" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M129.63125 121.03124999999999c17.306250000000002 14.29375 35.90625 27.450000000000003 50.44375 37.193749999999994" stroke-width="20"></path>
													<path id="Vector 2497" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M214.9 145.175c7.75 -24.712500000000002 16.5 -48.4375 23.325000000000003 -66.025" stroke-width="20"></path>
													<path id="Vector" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M18.75 18.75c0 101.90625 1.8812499999999999 201.93124999999998 2.725 241.5625a18.575 18.575 0 0 0 18.212500000000002 18.212500000000002C79.31875000000001 279.36875 179.34375 281.25 281.25 281.25" stroke-width="20"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Statistic Pages</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  Video to Article ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-10 -10 320 320" id="Camera-Video--Streamline-Plump" height="320" width="320">
												<g id="camera-video--film-television-tv-camera-movies-video-recorder">
													<path id="Rectangle 1099" stroke="#000000" stroke-linejoin="round" d="M20.4515625 250.976875c1.196625 15.886875 13.6405 27.660625 29.5465625 28.57C65.47 280.43125 88.078125 281.25 118.75 281.25c30.671875 0 53.28000000000001 -0.8187500000000001 68.751875 -1.7031250000000002 15.90625 -0.9093749999999999 28.349999999999998 -12.683125 29.54625 -28.57C217.97375 238.689375 218.75 221.866875 218.75 200c0 -21.866875 -0.77625 -38.689375 -1.7018749999999998 -50.976875 -1.1962499999999998 -15.886875 -13.639999999999999 -27.660625 -29.54625 -28.57C172.03 119.56875 149.421875 118.75 118.75 118.75c-30.671875 0 -53.28000000000001 0.8187500000000001 -68.751875 1.7031250000000002 -15.9060625 0.9093749999999999 -28.349875 12.683125 -29.5465625 28.57C19.5260625 161.310625 18.75 178.133125 18.75 200c0 21.866875 0.7760625 38.689375 1.7015624999999999 50.976875Z" stroke-width="20"></path>
													<path id="Subtract" stroke="#000000" stroke-linejoin="round" d="M218.09124999999997 233.0575c0.4 -9.388125 0.66125 -20.374375 0.66125 -33.0575 0 -12.688125 -0.26125 -23.678125 -0.661875 -33.069375 10.216875 -5.445625 24.62625 -12.545625 38.95375 -17.658125 11.258125 -4.016875000000001 21.950625000000002 2.84125 22.949375 14.7525 0.70875 8.45875 1.25625 20.120625 1.25625 35.96875 0 15.84875 -0.5475 27.510625 -1.25625 35.969375 -0.99875 11.911249999999999 -11.69125 18.769375 -22.949375 14.7525 -14.3275 -5.1125 -28.736250000000002 -12.2125 -38.953125 -17.658125Z" stroke-width="20"></path>
													<path id="Ellipse 40" stroke="#000000" stroke-linejoin="round" d="M43.75 53.125a34.375 34.375 0 1 0 68.75 0 34.375 34.375 0 1 0 -68.75 0" stroke-width="20"></path>
													<path id="Ellipse 41" stroke="#000000" stroke-linejoin="round" d="M143.75 62.5a25 25 0 1 0 50 0 25 25 0 1 0 -50 0" stroke-width="20"></path>
													<path id="Rectangle 60" stroke="#000000" stroke-linejoin="round" d="M143.8925 166.569375c0.20625000000000002 -5.39125 3.69875 -9.500625 9.07375 -9.961250000000001C155.435 156.396875 158.565625 156.25 162.5 156.25c3.9343749999999997 0 7.065 0.146875 9.533750000000001 0.35812499999999997 5.375 0.460625 8.8675 4.569999999999999 9.07375 9.961250000000001 0.085625 2.230625 0.14250000000000002 5.00375 0.14250000000000002 8.430625s-0.056875 6.2 -0.14250000000000002 8.430625c-0.20625000000000002 5.39125 -3.69875 9.500625 -9.07375 9.961250000000001 -2.46875 0.21125 -5.599375 0.35812499999999997 -9.533750000000001 0.35812499999999997 -3.9343749999999997 0 -7.065 -0.146875 -9.533750000000001 -0.35812499999999997 -5.375 -0.460625 -8.8675 -4.569999999999999 -9.07375 -9.961250000000001C143.806875 181.20000000000002 143.75 178.426875 143.75 175s0.056875 -6.2 0.14250000000000002 -8.430625Z" stroke-width="20"></path>
												</g>
											</svg>
											<span className="link-text ml-4">Video to Article</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  Google Map SEO Tracker  ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-10 -10 320 320" id="Location-Pin-3--Streamline-Plump" height="320" width="320">
												<g id="location-pin-3--navigation-map-maps-pin-gps-location">
													<path id="Rectangle 180" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m234.375 207.875 3.6249999999999996 0.2c7.593750000000001 0.42500000000000004 14.381250000000001 4.58125 17.8 11.375a209.50625 209.50625 0 0 1 15.14375 39.875c2.75 10.35625 -4.84375 19.9375 -15.543750000000001 20.375C237.48749999999998 280.4375 205.14999999999998 281.25 150 281.25c-55.15 0 -87.4875 -0.8125 -105.4 -1.54375 -10.7 -0.43750000000000006 -18.2875 -10.03125 -15.543750000000001 -20.375a209.51875 209.51875 0 0 1 15.14375 -39.88125c3.41875 -6.79375 10.20625 -10.95 17.8 -11.375l3.6249999999999996 -0.19375" stroke-width="20"></path>
													<path id="Ellipse 517" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M243.75 112.9375c0 66.4375 -67.175 114.12500000000001 -87.8 127.20625000000001a11.0375 11.0375 0 0 1 -11.899999999999999 0C123.42500000000001 227.06875000000002 56.25 179.375 56.25 112.9375 56.25 60.91875 98.225 18.75 150 18.75c51.775000000000006 0 93.75 42.16875 93.75 94.1875Z" stroke-width="20"></path>
													<path id="Ellipse 27" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M187.5 112.5a37.5 37.5 0 1 1 -75 0 37.5 37.5 0 0 1 75 0Z" stroke-width="20"></path>
												</g>
											</svg>
											<span className="link-text ml-4">GMB SEO Tracker</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  AI SEO Tracker  ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-10 -10 320 320" id="Class-Lesson--Streamline-Plump" height="320" width="320">
												<g id="class-lesson--class-lesson-education-teacher">
													<path id="Rectangle 1097" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M156.25 187.49375c49.9375 -0.10625000000000001 83.575 -1.45625 103.2 -2.5875 12.975 -0.75 23.45 -9.94375 24.95 -22.85C285.98125 148.5 287.5 128.125 287.5 100s-1.51875 -48.5 -3.1 -62.0625c-1.5 -12.9 -11.975 -22.09375 -24.94375 -22.84375C239.01875 13.9125 203.39374999999998 12.5 150 12.5c-4.28125 0 -8.450000000000001 0.00625 -12.5 0.025" stroke-width="20"></path>
													<path id="Vector 1001" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M78.125 212.5v68.75" stroke-width="20"></path>
													<path id="Ellipse 136" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M37.5 59.375a40.625 40.625 0 1 0 81.25 0 40.625 40.625 0 0 0 -81.25 0Z" stroke-width="20"></path>
													<path id="Union" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M178.36875 102.11250000000001c3.90625 0.22499999999999998 7.449999999999999 2.36875 8.2875 6.1875 0.8999999999999999 4.1000000000000005 1.4125 10.46875 -0.21875000000000003 19.39375 -0.8937499999999999 4.8812500000000005 -5.05625 8.387500000000001 -9.975000000000001 9.0625L125 143.75l-11.25 120.75c-0.75 8.025 -6.5625 14.64375 -14.5625 15.562500000000002a181.6875 181.6875 0 0 1 -20.64375 1.1875c-7.893749999999999 0 -15.01875 -0.525 -20.7375 -1.15625 -8.3625 -0.9312499999999999 -14.374999999999998 -7.968749999999999 -14.83125 -16.375l-3.8625 -70.73125a420.28749999999997 420.28749999999997 0 0 1 -16.1125 -1.14375 11.375 11.375 0 0 1 -10.337499999999999 -11.80625c0.7312500000000001 -23.5625 3.70625 -45.1 6.18125 -59.46875 1.8624999999999998 -10.8 11.0125 -18.60625 21.9625 -19.1375 43.487500000000004 -2.1125000000000003 92.75 -1.8875 137.5625 0.6875Z" stroke-width="20"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI SEO Tracker</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  ICP to Keyword Research  ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Ice-Cream-2--Streamline-Plump" height="48" width="48">
												<g id="ice-cream-2--cook-frozen-popsicle-freezer-nutrition-cream-stick-cold-ice-cooking">
													<path id="Rectangle 722" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M17.009 3.132c-4.409 0.184 -7.641 3.339 -7.845 7.746A177.192 177.192 0 0 0 9 19c0 3.359 0.067 6.023 0.164 8.122 0.204 4.407 3.436 7.562 7.845 7.746 1.88 0.079 4.188 0.132 6.99 0.132 2.804 0 5.113 -0.053 6.992 -0.132 4.409 -0.184 7.641 -3.338 7.845 -7.746 0.097 -2.098 0.164 -4.763 0.164 -8.122s-0.067 -6.024 -0.164 -8.122C38.632 6.47 35.4 3.316 30.99 3.132A167.282 167.282 0 0 0 24 3a168.2 168.2 0 0 0 -6.991 0.132Z" stroke-width="3"></path>
													<path id="Subtract" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m19.205 34.941 -0.612 4.58c-0.337 2.528 1.276 4.96 3.799 5.33 0.613 0.09 1.179 0.149 1.608 0.149 0.43 0 0.996 -0.06 1.609 -0.15 2.523 -0.37 4.136 -2.801 3.798 -5.329l-0.611 -4.58" stroke-width="3"></path>
													<path id="Vector 1416" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M19 13v12" stroke-width="3"></path>
													<path id="Vector 1417" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M29 13v12" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">ICP to KW Research</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  Auto Technical SEO Fixer  ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Cyborg-2--Streamline-Plump" height="48" width="48">
												<g id="cyborg-2--artificial-robotics-intelligence-machine-technology-android">
													<path id="Rectangle 1095" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M41.666 40.129c-0.211 2.534 -2.209 4.398 -4.747 4.547C34.13 44.839 29.892 45 24 45s-10.13 -0.16 -12.919 -0.324c-2.538 -0.15 -4.536 -2.014 -4.747 -4.547C6.156 37.986 6 34.977 6 31c0 -3.977 0.156 -6.985 0.334 -9.129 0.211 -2.533 2.209 -4.398 4.747 -4.547C13.87 17.161 18.108 17 24 17s10.13 0.16 12.919 0.324c2.538 0.15 4.536 2.013 4.747 4.547 0.178 2.144 0.334 5.152 0.334 9.129 0 3.977 -0.156 6.986 -0.334 9.129Z" stroke-width="3"></path>
													<path id="Ellipse 23" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M28 7a4 4 0 1 1 -8 0 4 4 0 0 1 8 0Z" stroke-width="3"></path>
													<path id="Vector 857" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M24 11v6" stroke-width="3"></path>
													<path id="Vector 839" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M17 26v1" stroke-width="3"></path>
													<path id="Vector 840" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M31 26v1" stroke-width="3"></path>
													<path id="Rectangle 1096" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M30 36s-1.886 2 -6 2 -6 -2 -6 -2" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Technical SEO</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* -------------------------  AI Internal Link Updater ------------------------- */}
								<li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to={isProduction ? "" : pageURL['websiteScanning']} className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="File-Check-Alternate--Streamline-Plump" height="48" width="48">
												<g id="file-check-alternate--file-common-check">
													<path id="Subtract" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M29.14 42.987c5.119 -0.063 8.743 -0.344 11.108 -0.614 2.2 -0.252 3.85 -1.903 4.092 -4.105 0.319 -2.89 0.66 -7.741 0.66 -15.268 0 -2.876 -0.05 -5.361 -0.13 -7.496 -0.351 -0.982 -1.55 -3.568 -5.266 -7.21 -3.941 -3.864 -6.67 -4.952 -7.519 -5.217A161.796 161.796 0 0 0 27 3c-6.264 0 -10.566 0.32 -13.248 0.627 -2.201 0.252 -3.85 1.903 -4.092 4.105 -0.225 2.034 -0.46 5.04 -0.58 9.268" stroke-width="3"></path>
													<path id="Intersect" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M44.884 15.96c-2.087 0.31 -5.84 0.072 -8.688 -0.192a4.664 4.664 0 0 1 -4.228 -4.21c-0.269 -2.803 -0.507 -6.468 -0.181 -8.489" stroke-width="3"></path>
													<path id="Ellipse 18" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3 33a12 12 0 1 0 24 0 12 12 0 1 0 -24 0" stroke-width="3"></path>
													<path id="Vector 972" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m20 29 -5.882 8L10 32.333" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Internal Links</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li>

								{/* ------------------------- Blog Author FINDER ------------------------- */}
								{/* <li className="mt-2 autoArticles subcategory-menu">
									<NavLink to={isProduction ? "" : pageURL['blogFinder']} className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="No-Poverty--Streamline-Plump" height="48" width="48">
												<g id="no-poverty"><path id="Ellipse 138" fill="" d="M40 9c0 3.3137 -2.6863 6 -6 6s-6 -2.6863 -6 -6c0 -3.31371 2.6863 -6 6 -6s6 2.68629 6 6Z" stroke-width="3"></path>
													<path id="Ellipse 139" fill="" d="M8 9c0 3.3137 2.6863 6 6 6s6 -2.6863 6 -6c0 -3.31371 -2.6863 -6 -6 -6S8 5.68629 8 9Z" stroke-width="3"></path>
													<path id="Ellipse 140" fill="" d="M18 17c0 3.3137 2.6863 6 6 6s6 -2.6863 6 -6 -2.6863 -6 -6 -6 -6 2.6863 -6 6Z" stroke-width="3"></path>
													<path id="Union" fill="" d="M28.8556 23.1174c2.0006 0.1073 3.5871 1.5579 3.9378 3.5305 0.3226 1.8145 0.7014 4.2829 0.9782 7.1199 0.1543 1.581 -1.0049 2.9535 -2.5907 3.0454 -0.4581 0.0266 -0.972 0.0527 -1.5418 0.0766l-0.3968 5.374c-0.0979 1.3261 -1.0588 2.4288 -2.3806 2.5738 -0.8188 0.0899 -1.8067 0.1624 -2.8615 0.1624s-2.0426 -0.0725 -2.8614 -0.1624c-1.3218 -0.145 -2.2827 -1.2477 -2.3806 -2.5738l-0.3969 -5.374c-0.5697 -0.0239 -1.0835 -0.05 -1.5416 -0.0766 -1.5858 -0.0918 -2.7448 -1.4644 -2.5906 -3.0453 0.2768 -2.8372 0.6557 -5.3056 0.9783 -7.1202 0.3507 -1.9725 1.937 -3.423 3.9376 -3.5303C20.3802 23.0512 21.986 23 24 23c2.0142 0 3.6202 0.0512 4.8556 0.1174Z" stroke-width="3"></path>
													<path id="Subtract" fill="" d="M18 16.9999c0 3.3137 2.6863 6 6 6 -2.014 0 -3.6201 0.0511 -4.8553 0.1174 -2.0005 0.1073 -3.5868 1.5578 -3.9375 3.5303 -0.3227 1.8146 -0.7015 4.283 -0.9783 7.1202 -0.1543 1.5809 1.0048 2.9534 2.5906 3.0453 0.4581 0.0265 0.9719 0.0526 1.5416 0.0766l0.3968 5.3739c0.0383 0.5174 0.2078 1.0008 0.4789 1.4065 -0.4247 0.311 -0.8519 0.606 -1.2913 0.8424 -0.3522 0.1894 -0.7436 0.3129 -1.1648 0.3546 -0.7753 0.0768 -1.6916 0.1328 -2.7153 0.1328 -0.9933 0 -1.8856 -0.0527 -2.646 -0.126 -1.67643 -0.1615 -2.87699 -1.6146 -3.02305 -3.3647l-0.80879 -9.6905c-0.67975 -0.0378 -1.29041 -0.0947 -1.82856 -0.1603 -1.71001 -0.2085 -2.85272 -1.7751 -2.75295 -3.5684 0.17661 -3.1742 0.59468 -6.1382 0.98739 -8.3852 0.43891 -2.5112 2.4526 -4.3321 4.89592 -4.4611 3.14234 -0.166 5.89324 -0.219 9.44924 -0.2359 -0.2193 0.6233 -0.3386 1.2938 -0.3386 1.9921Z" stroke-width="3"></path>
													<path id="Subtract_2" fill="" d="M29.6614 15.0078c0.2193 0.6233 0.3386 1.2938 0.3386 1.9921 0 3.3137 -2.6863 6 -6 6 2.0142 0 3.62 0.0511 4.8554 0.1174 2.0006 0.1073 3.587 1.5579 3.9377 3.5305 0.3227 1.8145 0.7015 4.2828 0.9783 7.1199 0.1542 1.581 -1.0049 2.9535 -2.5907 3.0454 -0.4581 0.0265 -0.972 0.0526 -1.5418 0.0765l-0.3968 5.3741c-0.0382 0.5173 -0.2078 1.0007 -0.4789 1.4064 0.4247 0.311 0.8519 0.606 1.2913 0.8424 0.3522 0.1894 0.7436 0.3129 1.1648 0.3546 0.7753 0.0768 1.6916 0.1328 2.7153 0.1328 0.9933 0 1.8856 -0.0527 2.646 -0.126 1.6764 -0.1615 2.877 -1.6146 3.023 -3.3647l0.8088 -9.6905c0.6798 -0.0378 1.2905 -0.0947 1.8286 -0.1603 1.71 -0.2085 2.8527 -1.7751 2.7529 -3.5684 -0.1766 -3.1742 -0.5946 -6.1382 -0.9873 -8.3852 -0.439 -2.5112 -2.4526 -4.3321 -4.896 -4.4611 -3.1423 -0.166 -5.8932 -0.219 -9.4492 -0.2359Z" stroke-width="3"></path>
													<path id="Ellipse 135" stroke="#000000" stroke-linejoin="round" d="M28 9a6 6 0 1 0 12 0 6 6 0 1 0 -12 0" stroke-width="3"></path><path id="Ellipse 136" stroke="#000000" stroke-linejoin="round" d="M20 9A6 6 0 1 1 8 9a6 6 0 1 1 12 0" stroke-width="3"></path><path id="Ellipse 137" fill="" stroke="#000000" stroke-linejoin="round" d="M30 17a6 6 0 1 1 -12 0 6 6 0 1 1 12 0" stroke-width="3"></path><path id="Subtract_3" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M18.3386 15.0078c-3.556 0.0169 -6.3069 0.0699 -9.44924 0.2359 -2.44332 0.129 -4.45701 1.9499 -4.89592 4.4611 -0.39271 2.247 -0.81078 5.211 -0.98739 8.3852 -0.09977 1.7933 1.04294 3.3599 2.75295 3.5684 0.53815 0.0656 1.14881 0.1225 1.82856 0.1603l0.80879 9.6905c0.14606 1.7501 1.34662 3.2032 3.02305 3.3647 0.7604 0.0733 1.6527 0.126 2.646 0.126 1.0237 0 1.94 -0.056 2.7153 -0.1328 0.9745 -0.0965 1.7893 -0.6308 2.3288 -1.4068" stroke-width="3"></path>
													<path id="Subtract_4" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M28.8906 43.4603c0.5395 0.776 1.3543 1.3104 2.3287 1.4068 0.7753 0.0768 1.6917 0.1328 2.7154 0.1328 0.9933 0 1.8856 -0.0527 2.646 -0.126 1.6764 -0.1615 2.877 -1.6146 3.023 -3.3647l0.8088 -9.6905c0.6798 -0.0378 1.2904 -0.0947 1.8286 -0.1603 1.71 -0.2085 2.8527 -1.7751 2.7529 -3.5684 -0.1766 -3.1742 -0.5946 -6.1382 -0.9873 -8.3852 -0.439 -2.5112 -2.4527 -4.3321 -4.896 -4.4611 -3.1423 -0.166 -5.8933 -0.219 -9.4492 -0.2359" stroke-width="3"></path><path id="Union_2" fill="" stroke="#000000" stroke-linejoin="round" d="M28.8556 23.1174c2.0006 0.1073 3.5871 1.5579 3.9378 3.5305 0.3226 1.8145 0.7014 4.2829 0.9782 7.1199 0.1543 1.581 -1.0049 2.9535 -2.5907 3.0454 -0.4581 0.0266 -0.972 0.0527 -1.5418 0.0766l-0.3968 5.374c-0.0979 1.3261 -1.0588 2.4288 -2.3806 2.5738 -0.8188 0.0899 -1.8067 0.1624 -2.8615 0.1624s-2.0426 -0.0725 -2.8614 -0.1624c-1.3218 -0.145 -2.2827 -1.2477 -2.3806 -2.5738l-0.3969 -5.374c-0.5697 -0.0239 -1.0835 -0.05 -1.5416 -0.0766 -1.5858 -0.0918 -2.7448 -1.4644 -2.5906 -3.0453 0.2768 -2.8372 0.6557 -5.3056 0.9783 -7.1202 0.3507 -1.9725 1.937 -3.423 3.9376 -3.5303C20.3802 23.0512 21.986 23 24 23c2.0142 0 3.6202 0.0512 4.8556 0.1174Z" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">Blog Author Finder</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li> */}

								{/* -------------------------  AI Reel Maker  ------------------------- */}
								{/* <li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Recording-Tape-1--Streamline-Plump" height="48" width="48">
												<g id="recording-tape-1--film-television-tv-movies-reel-video-entertainment">
													<path id="Ellipse 209" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3 22a19 19 0 1 0 38 0 19 19 0 1 0 -38 0" stroke-width="3"></path>
													<path id="Ellipse 211" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M18 12a4 4 0 1 0 8 0 4 4 0 1 0 -8 0" stroke-width="3"></path>
													<path id="Ellipse 213" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M32 18a4 4 0 1 0 0 8 4 4 0 1 0 0 -8" stroke-width="3"></path>
													<path id="Ellipse 212" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M18 32a4 4 0 1 1 8 0 4 4 0 1 1 -8 0" stroke-width="3"></path>
													<path id="Ellipse 214" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M12 18a4 4 0 1 0 0 8 4 4 0 1 0 0 -8" stroke-width="3"></path>
													<path id="Vector 1642" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M41 22c0 7 -4 14.3333 -4 18 0 3.6667 2.2 5 4 5 2.7 0 4 -2.2917 4 -7.3333" stroke-width="3"></path>
													<path id="Ellipse 431" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M22 22.5c-0.2761 0 -0.5 -0.2239 -0.5 -0.5s0.2239 -0.5 0.5 -0.5" stroke-width="3"></path>
													<path id="Ellipse 433" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M22 22.5c0.2761 0 0.5 -0.2239 0.5 -0.5s-0.2239 -0.5 -0.5 -0.5" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">AI Reel Maker</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li> */}

								{/* -------------------------  Email Verifier  ------------------------- */}
								{/* <li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Mail-Outgoing--Streamline-Plump" height="48" width="48">
												<g id="mail-outgoing--inbox-envelope-email-message-up-arrow-outbox">
													<path id="Rectangle 1097" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M37.66 11.5c1.682 0.95 3.183 1.85 4.302 2.54 1.32 0.813 2.223 2.128 2.416 3.667 0.283 2.26 0.622 6.196 0.622 12.093 0 4.569 -0.203 7.96 -0.426 10.308 -0.24 2.517 -2.249 4.349 -4.773 4.498C36.577 44.797 31.41 45 24 45c-7.409 0 -12.577 -0.203 -15.801 -0.394 -2.524 -0.149 -4.533 -1.981 -4.772 -4.498C3.203 37.76 3 34.368 3 29.8c0 -5.897 0.339 -9.833 0.622 -12.093 0.193 -1.539 1.096 -2.854 2.416 -3.667 1.12 -0.69 2.62 -1.59 4.303 -2.54" stroke-width="3"></path>
													<path id="Union" stroke="#000000" stroke-linejoin="round" d="M15.585 10.224c-0.853 1.006 -0.3 2.277 1.015 2.363 0.959 0.063 2.24 0.12 3.935 0.155a205.3 205.3 0 0 0 -0.035 4.032l0 5.594c0 1.076 0.598 2.063 1.658 2.252 0.5 0.09 1.115 0.154 1.842 0.154 0.728 0 1.342 -0.065 1.843 -0.154 1.06 -0.189 1.657 -1.176 1.657 -2.252v-5.594c0 -1.694 -0.014 -3.013 -0.034 -4.032a88.734 88.734 0 0 0 3.934 -0.155c1.316 -0.086 1.868 -1.357 1.015 -2.364a60.586 60.586 0 0 0 -2.96 -3.213c-2.05 -2.09 -3.522 -3.169 -4.416 -3.713a1.966 1.966 0 0 0 -2.077 0c-0.894 0.544 -2.366 1.624 -4.416 3.713a60.52 60.52 0 0 0 -2.96 3.214Z" stroke-width="3"></path>
													<path id="Rectangle 1099" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M39 19v2.727c0 1.738 -0.898 3.351 -2.413 4.204C33.203 27.836 27.113 31 24 31c-3.113 0 -9.203 -3.164 -12.588 -5.07C9.899 25.079 9 23.466 9 21.728L9 19" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">Email Verifier</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li> */}

								{/* -------------------------  Email Finder  ------------------------- */}
								{/* <li className="mt-2 autoArticles subcategory-menu pricing-plan">
									<NavLink to="" className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
										onClick={(e) => {
											if (isProduction) {
												e.preventDefault();
												return false;
											}
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Mail-Search--Streamline-Plump" height="48" width="48">
												<g id="mail-search--inbox-envelope-email-message-search">
													<path id="Ellipse 22" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M26 34.5a8.5 8.5 0 1 0 17 0 8.5 8.5 0 1 0 -17 0" stroke-width="3"></path>
													<path id="Vector 2310" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="m45 45 -4 -4" stroke-width="3"></path>
													<path id="Rectangle 1100" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M40.985 22.5c0.01 -0.796 0.015 -1.629 0.015 -2.5 0 -5.895 -0.245 -10.038 -0.48 -12.656 -0.19 -2.125 -1.84 -3.724 -3.967 -3.885C33.657 3.24 28.903 3 22 3c-6.903 0 -11.657 0.24 -14.553 0.46C5.32 3.62 3.67 5.22 3.48 7.343 3.245 9.962 3 14.105 3 20c0 5.895 0.245 10.038 0.48 12.656 0.19 2.125 1.84 3.724 3.967 3.885 2.754 0.208 7.188 0.436 13.553 0.457" stroke-width="3"></path>
													<path id="Intersect" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3.25 14c1.172 0.531 13.372 6 18.75 6s17.579 -5.47 18.75 -6" stroke-width="3"></path>
												</g>
											</svg>
											<span className="link-text ml-4">Email Finder</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
											Coming Soon
										</span>
									</NavLink>
								</li> */}


								{/* ------------------------- AI Keyword Results ------------------------- */}
								{/* <li className="mt-2 articles subcategory-menu">
								<NavLink to={pageURL['showKeywords']}
									onClick={checkForUnsavedChanges} className={({ isActive }) => isActive ? "is-active" : ""}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
										<g>
											<path id="Vector" fill="" d="M7 36.3529c0.3697 6.1654 -2.5 7.4118 -4 7.4118C3 43.7647 9.5 45 19 45c4.4849 0 8.3011 -0.2753 11.0805 -0.566 2.8661 -0.2997 4.9294 -2.7553 4.8495 -5.6359C34.6912 30.1945 33.9035 22.81 33.5424 13c-0.1126 -3.06039 0 -9 4.4576 -10L12 3C8.68629 3 6.00548 5.68197 6.05053 8.99537 6.13668 15.3307 6.3697 25.8418 7 36.3529Z" strokeWidth="3" />
											<path id="Subtract" fill="" d="M37.9998 3c-4.4569 0.99986 -4.5702 6.93788 -4.4576 9.9987 4.3993 -0.0219 7.398 -0.3069 9.2207 -0.5631 1.3335 -0.1874 2.2369 -1.3427 2.2369 -2.68934v-0.6487c0 -3.90244 -2.9999 -6.09756 -6 -6.09756l-1 0Z" strokeWidth="3" />
											<path id="Vector_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M7 36.3529c0.3697 6.1654 -2.5 7.4118 -4 7.4118C3 43.7647 9.5 45 19 45c4.4849 0 8.3011 -0.2753 11.0805 -0.566 2.8661 -0.2997 4.9294 -2.7553 4.8495 -5.6359C34.6912 30.1945 33.9035 22.81 33.5424 13c-0.1126 -3.06039 0 -9 4.4576 -10L12 3C8.68629 3 6.00548 5.68197 6.05053 8.99537 6.13668 15.3307 6.3697 25.8418 7 36.3529Z" strokeWidth="3" />
											<path id="Subtract_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m37.9996 3.00001 1 -0.00001c3.0001 0 6 2.19512 6 6.09756v0.6487c0 1.34664 -0.9034 2.50194 -2.2369 2.68934 -1.8227 0.2562 -4.8214 0.5412 -9.2207 0.5631" strokeWidth="3" />
											<path id="Vector 1679" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M17 13h10" strokeWidth="3" /><path id="Vector 1680" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m13 20 14 0" strokeWidth="3" />
											<path id="Vector 1681" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m13.5 27 14 0" strokeWidth="3" /><path id="Vector 1682" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m14 34 14 0" strokeWidth="3" />
										</g>
									</svg>
									<span className="link-text ml-4">AI Keyword Results</span>
								</NavLink>
							</li> */}


								{/* ------------------------- INDEXATION ------------------------- */}
								{/* <li className={"mt-2 indexation category-menu"}>
								{props.currentPlanName === "Trial" ?
									<NavLink to={pageURL['manageSubscription']} className={"upgrade"} onClick={checkForUnsavedChanges}>
										<div className="category">
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
												<g>
													<path id="hover" fill="" d="M20.746 26.794a97.66 97.66 0 0 1 -6.657 -1.038c-1.442 -0.28 -1.987 -1.956 -1.005 -3.049 2.512 -2.797 7.006 -7.605 11.87 -11.883 1.29 -1.134 3.235 -0.14 3.116 1.574 -0.25 3.597 -0.543 6.527 -0.815 8.808 2.75 0.336 5.009 0.72 6.656 1.038 1.443 0.28 1.987 1.956 1.006 3.049 -2.512 2.796 -7.006 7.605 -11.87 11.883 -1.29 1.134 -3.235 0.14 -3.116 -1.574 0.25 -3.597 0.542 -6.527 0.815 -8.808Z" strokeWidth="3" />
													<path id="Ellipse 32" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M44.788 21C43.332 10.823 34.58 3 24 3S4.67 10.823 3.213 21" strokeWidth="3" />
													<path id="Ellipse 582" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M27 44.787a20.847 20.847 0 0 0 4.798 -1.282M21 44.787a20.81 20.81 0 0 1 -4.829 -1.295M44.788 27a20.855 20.855 0 0 1 -1.283 4.797M3.213 27c0.239 1.67 0.674 3.278 1.282 4.797m6.515 8.704a21.125 21.125 0 0 1 -3.531 -3.536m33.043 0a21.126 21.126 0 0 1 -3.558 3.557" strokeWidth="3" />
													<path id="Union_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M20.746 26.794a97.66 97.66 0 0 1 -6.657 -1.038c-1.442 -0.28 -1.987 -1.956 -1.005 -3.049 2.512 -2.797 7.006 -7.605 11.87 -11.883 1.29 -1.134 3.235 -0.14 3.116 1.574 -0.25 3.597 -0.543 6.527 -0.815 8.808 2.75 0.336 5.009 0.72 6.656 1.038 1.443 0.28 1.987 1.956 1.006 3.049 -2.512 2.796 -7.006 7.605 -11.87 11.883 -1.29 1.134 -3.235 0.14 -3.116 -1.574 0.25 -3.597 0.542 -6.527 0.815 -8.808Z" strokeWidth="3" />
												</g>
											</svg>
											<span className="link-text ml-4">
												Fast Google Index
											</span>
										</div>
										<span className="uncollapsed-tag tag is-info is-light is-rounded">
											Upgrade ⚡
										</span>
									</NavLink>
									:
									<NavLink to={pageURL['indexation']}
										onClick={checkForUnsavedChanges} className={({ isActive }) => isActive ? "is-active " : ""}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
											<g>
												<path id="hover" fill="" d="M20.746 26.794a97.66 97.66 0 0 1 -6.657 -1.038c-1.442 -0.28 -1.987 -1.956 -1.005 -3.049 2.512 -2.797 7.006 -7.605 11.87 -11.883 1.29 -1.134 3.235 -0.14 3.116 1.574 -0.25 3.597 -0.543 6.527 -0.815 8.808 2.75 0.336 5.009 0.72 6.656 1.038 1.443 0.28 1.987 1.956 1.006 3.049 -2.512 2.796 -7.006 7.605 -11.87 11.883 -1.29 1.134 -3.235 0.14 -3.116 -1.574 0.25 -3.597 0.542 -6.527 0.815 -8.808Z" strokeWidth="3" />
												<path id="Ellipse 32" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M44.788 21C43.332 10.823 34.58 3 24 3S4.67 10.823 3.213 21" strokeWidth="3" />
												<path id="Ellipse 582" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M27 44.787a20.847 20.847 0 0 0 4.798 -1.282M21 44.787a20.81 20.81 0 0 1 -4.829 -1.295M44.788 27a20.855 20.855 0 0 1 -1.283 4.797M3.213 27c0.239 1.67 0.674 3.278 1.282 4.797m6.515 8.704a21.125 21.125 0 0 1 -3.531 -3.536m33.043 0a21.126 21.126 0 0 1 -3.558 3.557" strokeWidth="3" />
												<path id="Union_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M20.746 26.794a97.66 97.66 0 0 1 -6.657 -1.038c-1.442 -0.28 -1.987 -1.956 -1.005 -3.049 2.512 -2.797 7.006 -7.605 11.87 -11.883 1.29 -1.134 3.235 -0.14 3.116 1.574 -0.25 3.597 -0.543 6.527 -0.815 8.808 2.75 0.336 5.009 0.72 6.656 1.038 1.443 0.28 1.987 1.956 1.006 3.049 -2.512 2.796 -7.006 7.605 -11.87 11.883 -1.29 1.134 -3.235 0.14 -3.116 -1.574 0.25 -3.597 0.542 -6.527 0.815 -8.808Z" strokeWidth="3" />
											</g>
										</svg>
										<span className="link-text ml-4">Fast Google Index</span>
									</NavLink>
								}
							</li> */}

								{/* ------------------------- Feature Request Form ------------------------- */}
								{/* <li className="mt-2 subcategory-menu pricing-plan"> */}
								{/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
								{/* <a onClick={() => setFeatureRequestModalActive(true)}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.75 -0.75 24 24" height="24" width="24">
										<g>
											<path id="Union" d="M3.8566124999999998 18.550593749999997c-1.1900765625 -0.07621875 -2.1283499999999997 -0.953859375 -2.2371140625 -2.1414375C1.5097593749999998 15.210984374999999 1.40625 13.438640625 1.40625 11.015625c0 -2.423015625 0.103509375 -4.195359375000001 0.2132484375 -5.39353125 0.1087640625 -1.187559375 1.0470421875 -2.065228125 2.2371140625 -2.1414515625C5.36896875 3.3837796875 7.787531249999999 3.28125 11.25 3.28125s5.88103125 0.10252968750000001 7.39340625 0.199396875c1.1900625 0.0762234375 2.1283125 0.9538875 2.237109375 2.1414468749999997C20.990250000000003 6.820265624999999 21.09375 8.592609375 21.09375 11.015625c0 2.423015625 -0.1035 4.195359375000001 -0.213234375 5.39353125 -0.108796875 1.187578125 -1.047046875 2.06521875 -2.237109375 2.1414375 -1.4619843750000001 0.09365625 -3.770765625 0.1925625 -7.05084375 0.19907812500000002l-3.0136875 2.5831874999999997C7.9707187500000005 21.854109375 7.03125 21.422015625 7.03125 20.6210625v-1.928203125c-1.3245 -0.038343749999999996 -2.37594375 -0.091078125 -3.1746375000000002 -0.142265625Z" strokeWidth="1.5" />
											<path id="Rectangle 1845" fill="" d="M13.033078125 1.4044874999999999c-0.031265625 -0.259715625 -0.280453125 -0.380925 -0.5107968749999999 -0.25696875 -0.32071875 0.1725609375 -0.830859375 0.47074687499999995 -1.559015625 0.962034375 -0.597328125 0.402984375 -0.974578125 0.71934375 -1.21059375 0.9490921875 -0.27281249999999996 0.2655703125 -0.27285937499999996 0.6487640625000001 -0.000046875 0.9143484374999999 0.23601562499999998 0.2297671875 0.613265625 0.5461265625 1.210640625 0.94906875 0.728109375 0.49115625 1.23825 0.7893281249999999 1.558921875 0.9619218749999999 0.230390625 0.12398437500000001 0.4796250000000001 0.002765625 0.510890625 -0.25696875 0.045328125 -0.376546875 0.091921875 -1.02725625 0.091921875 -2.1112125 0 -1.08406875 -0.04659375 -1.7348249999999998 -0.091921875 -2.1113156249999996Z" strokeWidth="1.5" />
											<g>
												<path d="M12.65625 11.25c0 0.776671875 -0.629578125 1.40625 -1.40625 1.40625s-1.40625 -0.629578125 -1.40625 -1.40625 0.629578125 -1.40625 1.40625 -1.40625 1.40625 0.629578125 1.40625 1.40625Z" strokeWidth="1.5" />
												<path d="M17.8125 11.25c0 0.776671875 -0.629578125 1.40625 -1.40625 1.40625s-1.40625 -0.629578125 -1.40625 -1.40625 0.629578125 -1.40625 1.40625 -1.40625 1.40625 0.629578125 1.40625 1.40625Z" strokeWidth="1.5" />
												<path d="M7.5 11.25c0 0.776671875 -0.629578125 1.40625 -1.40625 1.40625s-1.40625 -0.629578125 -1.40625 -1.40625 0.629578125 -1.40625 1.40625 -1.40625 1.40625 0.629578125 1.40625 1.40625Z" strokeWidth="1.5" />
											</g>
											<path id="Union_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M13.125 3.291778125c2.480390625 0.028537499999999997 4.295625 0.11055937499999999 5.51840625 0.188878125 1.1900625 0.0762234375 2.1283125 0.9538921874999999 2.237109375 2.1414375C20.990250000000003 6.820265624999999 21.09375 8.592609375 21.09375 11.015625c0 2.423015625 -0.1035 4.1954062500000004 -0.213234375 5.3935781249999994 -0.108796875 1.18753125 -1.047046875 2.0651718750000003 -2.237109375 2.1414375 -1.4619843750000001 0.093609375 -3.770765625 0.1925625 -7.05084375 0.19903125l-3.0136875 2.5831874999999997C7.9707187500000005 21.854109375 7.03125 21.422015625 7.03125 20.6210625v-1.9281562500000002c-1.3245 -0.038390625 -2.37594375 -0.091125 -3.1746375000000002 -0.142265625 -1.1900765625 -0.076265625 -2.1283499999999997 -0.9539062500000001 -2.2371140625 -2.1414375C1.5097593749999998 15.211031250000001 1.40625 13.438640625 1.40625 11.015625c0 -2.42296875 0.103509375 -4.195359375000001 0.2132484375 -5.39353125 0.1087640625 -1.1875453125 1.0470421875 -2.0652140625 2.2371140625 -2.1414375C4.65530625 3.429496875 5.70675 3.3767625 7.03125 3.3383859375" strokeWidth="1.5" />
											<path id="Rectangle 1846" stroke="#000000" fill="" strokeLinecap="round" strokeLinejoin="round" d="M13.033078125 1.4044874999999999c-0.031265625 -0.259715625 -0.280453125 -0.380925 -0.5107968749999999 -0.25696875 -0.32071875 0.1725609375 -0.830859375 0.47074687499999995 -1.559015625 0.962034375 -0.597328125 0.402984375 -0.974578125 0.71934375 -1.21059375 0.9490921875 -0.27281249999999996 0.2655703125 -0.27285937499999996 0.6487640625000001 -0.000046875 0.9143484374999999 0.23601562499999998 0.2297671875 0.613265625 0.5461265625 1.210640625 0.94906875 0.728109375 0.49115625 1.23825 0.7893281249999999 1.558921875 0.9619218749999999 0.230390625 0.12398437500000001 0.4796250000000001 0.002765625 0.510890625 -0.25696875 0.045328125 -0.376546875 0.091921875 -1.02725625 0.091921875 -2.1112125 0 -1.08406875 -0.04659375 -1.7348249999999998 -0.091921875 -2.1113156249999996Z" strokeWidth="1.5" />
											<path id="Vector_2" stroke="#000000" fill="" strokeLinecap="round" strokeLinejoin="round" d="M7.5 11.25c0 0.776671875 -0.629578125 1.40625 -1.40625 1.40625s-1.40625 -0.629578125 -1.40625 -1.40625 0.629578125 -1.40625 1.40625 -1.40625 1.40625 0.629578125 1.40625 1.40625Z" strokeWidth="1.5" />
											<path id="Vector_3" stroke="#000000" fill="" strokeLinecap="round" strokeLinejoin="round" d="M12.65625 11.25c0 0.776671875 -0.629578125 1.40625 -1.40625 1.40625s-1.40625 -0.629578125 -1.40625 -1.40625 0.629578125 -1.40625 1.40625 -1.40625 1.40625 0.629578125 1.40625 1.40625Z" strokeWidth="1.5" />
											<path id="Vector_4" stroke="#000000" fill="" strokeLinecap="round" strokeLinejoin="round" d="M17.8125 11.25c0 0.776671875 -0.629578125 1.40625 -1.40625 1.40625s-1.40625 -0.629578125 -1.40625 -1.40625 0.629578125 -1.40625 1.40625 -1.40625 1.40625 0.629578125 1.40625 1.40625Z" strokeWidth="1.5" />
										</g>
									</svg>
									<span className="link-text ml-4">Feature Request</span>
								</a>
							</li> */}

								{/* ------------------------- Tutorials -------------------------
							<li className="mt-2 subcategory-menu pricing-plan">
								<NavLink to={pageURL['tutorials']} className={({ isActive }) => (isActive && !window.location.href.endsWith("?tab=integration")) ? "is-active settings" : "settings"}
									onClick={(e) => {
										checkForUnsavedChanges(e);
										let interval = setInterval(() => {
											const imagesTab = document.getElementById("images-tab");
											if (imagesTab) {
												imagesTab.click();
												clearInterval(interval);
											}
										}, 500);
									}}>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
										<g>
											<path id="Rectangle 1105" fill="" d="M3.539 8.257c0.208 -2.555 2.163 -4.51 4.718 -4.718C11.485 3.277 16.636 3 24 3c7.364 0 12.515 0.277 15.743 0.539 2.555 0.208 4.51 2.163 4.718 4.718C44.723 11.485 45 16.636 45 24c0 7.364 -0.277 12.515 -0.539 15.743 -0.208 2.555 -2.163 4.51 -4.718 4.718C36.515 44.723 31.364 45 24 45c-7.364 0 -12.515 -0.277 -15.743 -0.539 -2.555 -0.208 -4.51 -2.163 -4.718 -4.718C3.277 36.515 3 31.364 3 24c0 -7.364 0.277 -12.515 0.539 -15.743Z" strokeWidth="3"></path>
											<path id="Intersect" fill="" d="M44.806 14H3.196c0.098 -2.345 0.222 -4.25 0.344 -5.743 0.207 -2.555 2.163 -4.51 4.718 -4.718C11.486 3.277 16.637 3 24 3c7.364 0 12.514 0.277 15.743 0.539 2.555 0.208 4.51 2.163 4.718 4.718 0.121 1.494 0.245 3.398 0.344 5.743Z" strokeWidth="3"></path>
											<path id="Rectangle 1106" fill="" d="M17.353 23.873c0.25 -1.73 1.775 -2.714 3.405 -2.084 1.144 0.443 2.686 1.133 4.713 2.21 1.938 1.03 3.432 1.941 4.56 2.695 1.758 1.175 1.758 3.435 0 4.61 -1.128 0.754 -2.622 1.665 -4.56 2.695 -2.027 1.077 -3.569 1.767 -4.713 2.21 -1.63 0.63 -3.155 -0.354 -3.405 -2.084A35.992 35.992 0 0 1 17 28.999c0 -2.078 0.163 -3.81 0.353 -5.126Z" strokeWidth="3"></path>
											<path id="Rectangle 1104" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M3.539 8.257c0.208 -2.555 2.163 -4.51 4.718 -4.718C11.485 3.277 16.636 3 24 3c7.364 0 12.515 0.277 15.743 0.539 2.555 0.208 4.51 2.163 4.718 4.718C44.723 11.485 45 16.636 45 24c0 7.364 -0.277 12.515 -0.539 15.743 -0.208 2.555 -2.163 4.51 -4.718 4.718C36.515 44.723 31.364 45 24 45c-7.364 0 -12.515 -0.277 -15.743 -0.539 -2.555 -0.208 -4.51 -2.163 -4.718 -4.718C3.277 36.515 3 31.364 3 24c0 -7.364 0.277 -12.515 0.539 -15.743Z" strokeWidth="3"></path>
											<path id="Rectangle 128" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M17.353 23.874c0.25 -1.73 1.775 -2.714 3.405 -2.084 1.144 0.443 2.686 1.133 4.713 2.21 1.938 1.03 3.432 1.941 4.56 2.695 1.758 1.175 1.758 3.435 0 4.61 -1.128 0.754 -2.622 1.665 -4.56 2.695 -2.027 1.077 -3.569 1.767 -4.713 2.21 -1.63 0.63 -3.155 -0.354 -3.405 -2.084A35.992 35.992 0 0 1 17 29c0 -2.078 0.163 -3.81 0.353 -5.126Z" strokeWidth="3"></path>
											<path id="Intersect_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M44.806 14H3.196" strokeWidth="3"></path>
											<path id="Vector 167" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m20.289 3.15 -8.008 10.677" strokeWidth="3"></path>
											<path id="Vector 168" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="m36.717 3.378 -7.852 10.469" strokeWidth="3"></path>
										</g>
									</svg>
									<span className="link-text ml-4">Tutorials</span>
								</NavLink>
							</li> */}


							</ul>
						</div>

						<div className="mb-5 is-flex is-flex-direction-column">
							<div className="mt-5 is-size-7" style={{ marginLeft: '1.1rem' }}>
								Other Pages
							</div>
							<ul className="menu-list">

								{/* ------------------------- INTEGRATIONS ------------------------- */}
								<li className={"mt-2 autoArticles subcategory-menu"}>
									{props.currentPlanName === "Trial" ?
										<NavLink
											to={props.websiteList.length === 0 ? pageURL['connectWebsite'] : pageURL['new-integration']}
											className={({ isActive }) => `upgrade ${isActive ? 'is-active' : ''}`}
											onClick={(e) => {
												checkForUnsavedChanges(e);
												setHamburgerActive(false);
												let interval = setInterval(() => {
													const integrationTab = document.getElementById("integration-tab");
													if (integrationTab) {
														integrationTab.click();
														clearInterval(interval);
													}
												}, 500);
											}}>
											<div className="category">
												<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
													<g>
														<path id="Subtract" fill="" d="M20.376 37.884c0.193 2.371 0.413 4.547 0.539 5.74 0.064 0.606 0.425 1.119 1.024 1.225 0.463 0.082 1.128 0.15 2.061 0.15s1.599 -0.068 2.06 -0.15c0.6 -0.106 0.961 -0.62 1.025 -1.225 0.126 -1.193 0.346 -3.369 0.54 -5.74a61.832 61.832 0 0 1 -3.625 0.115c-1.248 0 -2.497 -0.048 -3.624 -0.115Z" strokeWidth="3" />
														<path id="hover" fill="" d="M39.056 16.6c1.565 0.176 2.703 1.401 2.822 2.971 0.136 1.796 0.216 4.243 -0.08 6.366a2.956 2.956 0 0 1 -0.969 1.806c-1.23 1.093 -3.931 3.217 -8.238 5.179a198.938 198.938 0 0 1 -0.382 3.084 1.905 1.905 0 0 1 -1.71 1.655C28.973 37.807 26.487 38 24 38c-2.486 0 -4.973 -0.193 -6.499 -0.34a1.905 1.905 0 0 1 -1.71 -1.654c-0.099 -0.747 -0.229 -1.775 -0.382 -3.084 -4.307 -1.962 -7.009 -4.086 -8.238 -5.179a2.956 2.956 0 0 1 -0.968 -1.806c-0.297 -2.123 -0.217 -4.57 -0.081 -6.366 0.119 -1.57 1.257 -2.795 2.822 -2.971 2.478 -0.28 7.069 -0.6 15.056 -0.6 7.987 0 12.578 0.322 15.056 0.6Z" strokeWidth="3" />
														<path id="Subtract_2" fill="" d="M29.144 5.742c0.066 -1.435 0.864 -2.658 2.299 -2.729a11.322 11.322 0 0 1 1.114 0c1.435 0.07 2.233 1.294 2.299 2.73C34.929 7.325 35 9.916 35 14c0 0.814 -0.003 1.57 -0.008 2.269a157.085 157.085 0 0 0 -5.986 -0.222C29.002 15.41 29 14.729 29 14c0 -4.083 0.071 -6.674 0.144 -8.258Z" strokeWidth="3" />
														<path id="Subtract_3" fill="" d="M18.856 5.742c-0.066 -1.435 -0.864 -2.658 -2.299 -2.729a11.322 11.322 0 0 0 -1.114 0c-1.435 0.07 -2.233 1.294 -2.299 2.73C13.071 7.325 13 9.916 13 14c0 0.814 0.003 1.57 0.008 2.269 1.615 -0.094 3.589 -0.174 5.986 -0.222 0.004 -0.637 0.006 -1.318 0.006 -2.047 0 -4.083 -0.071 -6.674 -0.144 -8.258Z" strokeWidth="3" />
														<path id="Union_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M39.056 16.6c1.565 0.176 2.703 1.401 2.822 2.971 0.136 1.796 0.216 4.243 -0.08 6.366a2.956 2.956 0 0 1 -0.969 1.806c-1.23 1.093 -3.931 3.217 -8.238 5.179a198.938 198.938 0 0 1 -0.382 3.084 1.905 1.905 0 0 1 -1.71 1.655C28.973 37.807 26.487 38 24 38c-2.486 0 -4.973 -0.193 -6.499 -0.34a1.905 1.905 0 0 1 -1.71 -1.654c-0.099 -0.747 -0.229 -1.775 -0.382 -3.084 -4.307 -1.962 -7.009 -4.086 -8.238 -5.179a2.956 2.956 0 0 1 -0.968 -1.806c-0.297 -2.123 -0.217 -4.57 -0.081 -6.366 0.119 -1.57 1.257 -2.795 2.822 -2.971 2.478 -0.28 7.069 -0.6 15.056 -0.6 7.987 0 12.578 0.322 15.056 0.6Z" strokeWidth="3" />
														<path id="Subtract_4" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M13.008 16.269C13.003 15.569 13 14.814 13 14c0 -4.083 0.071 -6.674 0.144 -8.258 0.066 -1.435 0.864 -2.658 2.299 -2.729a11.322 11.322 0 0 1 1.114 0c1.436 0.07 2.233 1.294 2.299 2.73C18.929 7.325 19 9.916 19 14c0 0.729 -0.002 1.41 -0.006 2.047" strokeWidth="3" />
														<path id="Subtract_5" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M29.006 16.047C29.002 15.41 29 14.729 29 14c0 -4.083 0.071 -6.674 0.144 -8.258 0.066 -1.435 0.864 -2.658 2.299 -2.729a11.322 11.322 0 0 1 1.114 0c1.435 0.07 2.233 1.294 2.299 2.73C34.929 7.325 35 9.916 35 14c0 0.814 -0.003 1.57 -0.008 2.269" strokeWidth="3" />
														<path id="Subtract_6" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M20.375 37.885c0.193 2.371 0.413 4.547 0.539 5.74 0.064 0.606 0.425 1.119 1.024 1.225 0.463 0.082 1.128 0.15 2.061 0.15s1.599 -0.068 2.06 -0.15c0.6 -0.106 0.961 -0.62 1.025 -1.225 0.126 -1.193 0.346 -3.369 0.54 -5.74" strokeWidth="3" />
													</g>
												</svg>
												<span className="link-text ml-4">
													Integrations
												</span>
											</div>
											{/* <span className="uncollapsed-tag tag is-info is-light is-rounded upgrade-menu ">
                                                Upgrade ⚡
                                            </span> */}
										</NavLink>
										:
										<NavLink
											to={props.websiteList.length === 0 ? pageURL['connectWebsite'] : pageURL['new-integration']}
											className={({ isActive }) => isActive ? "is-active" : ""}
											onClick={(e) => {
												checkForUnsavedChanges(e);
												setHamburgerActive(false);
												let interval = setInterval(() => {
													const integrationTab = document.getElementById("integration-tab");
													if (integrationTab) {
														integrationTab.click();
														clearInterval(interval);
													}
												}, 500);
											}}>
											<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
												<g>
													<path id="Subtract" fill="" d="M20.376 37.884c0.193 2.371 0.413 4.547 0.539 5.74 0.064 0.606 0.425 1.119 1.024 1.225 0.463 0.082 1.128 0.15 2.061 0.15s1.599 -0.068 2.06 -0.15c0.6 -0.106 0.961 -0.62 1.025 -1.225 0.126 -1.193 0.346 -3.369 0.54 -5.74a61.832 61.832 0 0 1 -3.625 0.115c-1.248 0 -2.497 -0.048 -3.624 -0.115Z" strokeWidth="3" />
													<path id="hover" fill="" d="M39.056 16.6c1.565 0.176 2.703 1.401 2.822 2.971 0.136 1.796 0.216 4.243 -0.08 6.366a2.956 2.956 0 0 1 -0.969 1.806c-1.23 1.093 -3.931 3.217 -8.238 5.179a198.938 198.938 0 0 1 -0.382 3.084 1.905 1.905 0 0 1 -1.71 1.655C28.973 37.807 26.487 38 24 38c-2.486 0 -4.973 -0.193 -6.499 -0.34a1.905 1.905 0 0 1 -1.71 -1.654c-0.099 -0.747 -0.229 -1.775 -0.382 -3.084 -4.307 -1.962 -7.009 -4.086 -8.238 -5.179a2.956 2.956 0 0 1 -0.968 -1.806c-0.297 -2.123 -0.217 -4.57 -0.081 -6.366 0.119 -1.57 1.257 -2.795 2.822 -2.971 2.478 -0.28 7.069 -0.6 15.056 -0.6 7.987 0 12.578 0.322 15.056 0.6Z" strokeWidth="3" />
													<path id="Subtract_2" fill="" d="M29.144 5.742c0.066 -1.435 0.864 -2.658 2.299 -2.729a11.322 11.322 0 0 1 1.114 0c1.435 0.07 2.233 1.294 2.299 2.73C34.929 7.325 35 9.916 35 14c0 0.814 -0.003 1.57 -0.008 2.269a157.085 157.085 0 0 0 -5.986 -0.222C29.002 15.41 29 14.729 29 14c0 -4.083 0.071 -6.674 0.144 -8.258Z" strokeWidth="3" />
													<path id="Subtract_3" fill="" d="M18.856 5.742c-0.066 -1.435 -0.864 -2.658 -2.299 -2.729a11.322 11.322 0 0 0 -1.114 0c-1.435 0.07 -2.233 1.294 -2.299 2.73C13.071 7.325 13 9.916 13 14c0 0.814 0.003 1.57 0.008 2.269 1.615 -0.094 3.589 -0.174 5.986 -0.222 0.004 -0.637 0.006 -1.318 0.006 -2.047 0 -4.083 -0.071 -6.674 -0.144 -8.258Z" strokeWidth="3" />
													<path id="Union_2" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M39.056 16.6c1.565 0.176 2.703 1.401 2.822 2.971 0.136 1.796 0.216 4.243 -0.08 6.366a2.956 2.956 0 0 1 -0.969 1.806c-1.23 1.093 -3.931 3.217 -8.238 5.179a198.938 198.938 0 0 1 -0.382 3.084 1.905 1.905 0 0 1 -1.71 1.655C28.973 37.807 26.487 38 24 38c-2.486 0 -4.973 -0.193 -6.499 -0.34a1.905 1.905 0 0 1 -1.71 -1.654c-0.099 -0.747 -0.229 -1.775 -0.382 -3.084 -4.307 -1.962 -7.009 -4.086 -8.238 -5.179a2.956 2.956 0 0 1 -0.968 -1.806c-0.297 -2.123 -0.217 -4.57 -0.081 -6.366 0.119 -1.57 1.257 -2.795 2.822 -2.971 2.478 -0.28 7.069 -0.6 15.056 -0.6 7.987 0 12.578 0.322 15.056 0.6Z" strokeWidth="3" />
													<path id="Subtract_4" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M13.008 16.269C13.003 15.569 13 14.814 13 14c0 -4.083 0.071 -6.674 0.144 -8.258 0.066 -1.435 0.864 -2.658 2.299 -2.729a11.322 11.322 0 0 1 1.114 0c1.436 0.07 2.233 1.294 2.299 2.73C18.929 7.325 19 9.916 19 14c0 0.729 -0.002 1.41 -0.006 2.047" strokeWidth="3" />
													<path id="Subtract_5" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M29.006 16.047C29.002 15.41 29 14.729 29 14c0 -4.083 0.071 -6.674 0.144 -8.258 0.066 -1.435 0.864 -2.658 2.299 -2.729a11.322 11.322 0 0 1 1.114 0c1.435 0.07 2.233 1.294 2.299 2.73C34.929 7.325 35 9.916 35 14c0 0.814 -0.003 1.57 -0.008 2.269" strokeWidth="3" />
													<path id="Subtract_6" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M20.375 37.885c0.193 2.371 0.413 4.547 0.539 5.74 0.064 0.606 0.425 1.119 1.024 1.225 0.463 0.082 1.128 0.15 2.061 0.15s1.599 -0.068 2.06 -0.15c0.6 -0.106 0.961 -0.62 1.025 -1.225 0.126 -1.193 0.346 -3.369 0.54 -5.74" strokeWidth="3" />
												</g>
											</svg>
											<span className="link-text ml-4">Integrations</span>
										</NavLink>
									}
								</li>

								{/* ------------------------- Updates  ------------------------- */}
								<li className="mt-2 subcategory-menu pricing-plan">
									<NavLink to={"/updates"} className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Ringing-Bell-Notification--Streamline-Plump" height="48" width="48">
											<g id="ringing-bell-notification--notification-vibrate-ring-sound-alarm-alert-bell-noise">
												<path id="Subtract" fill="" d="M16.048 37.878A235.7 235.7 0 0 0 24 38c3.05 0 5.683 -0.045 7.953 -0.122a8.001 8.001 0 0 1 -15.905 0Z" stroke-width="3"></path>
												<path id="Union" fill="" d="M39.195 16.949C38.52 9.059 31.918 3 24 3 16.08 3 9.48 9.06 8.804 16.949l-0.479 5.59a16 16 0 0 1 -2.326 7.036l-1.47 2.384c-1.478 2.393 -0.672 4.866 2.116 5.237C9.833 37.62 15.157 38 24 38c8.842 0 14.166 -0.38 17.355 -0.804 2.788 -0.371 3.594 -2.844 2.116 -5.237l-1.47 -2.384a16 16 0 0 1 -2.327 -7.037l-0.479 -5.59Z" stroke-width="3"></path>
												<path id="Union_2" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M39.196 16.949C38.519 9.059 31.918 3 24 3S9.48 9.06 8.804 16.949l-0.479 5.59A16 16 0 0 1 6 29.574l-1.472 2.384c-1.477 2.393 -0.671 4.866 2.117 5.237C9.833 37.62 15.157 38 24 38s14.166 -0.38 17.355 -0.804c2.788 -0.371 3.594 -2.844 2.117 -5.237L42 29.575a16.001 16.001 0 0 1 -2.325 -7.037l-0.48 -5.59Z" stroke-width="3"></path>
												<path id="Subtract_2" fill="" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M16.048 37.878a8.001 8.001 0 0 0 15.904 0" stroke-width="3"></path><path id="Vector 1499" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M3.015 17C2.849 14.333 4.013 7.8 10 3" stroke-width="3"></path>
												<path id="Vector 1500" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M44.985 17c0.166 -2.667 -0.998 -9.2 -6.985 -14" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Updates</span>
									</NavLink>
								</li>

								{/* ------------------------- MANAGE SUBSCRIPTION ------------------------- */}
								<li className="mt-2 articles subcategory-menu">
									<NavLink to={pageURL['manageSubscription']}
										className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" height="48" width="48">
											<g>
												<path id="Ellipse 20" fill="" d="M24 45c11.598 0 21 -9.402 21 -21S35.598 3 24 3 3 12.402 3 24s9.402 21 21 21Z" strokeWidth="3" />
												<path id="hover" fill="" d="M22.225 12.423c0.746 -1.439 2.804 -1.439 3.55 0l2.811 5.418 5.959 1.174c1.523 0.3 2.138 2.145 1.1 3.3l-4.225 4.698 0.776 6.288c0.196 1.584 -1.45 2.747 -2.878 2.035L24 32.68l-5.317 2.655c-1.428 0.712 -3.074 -0.451 -2.879 -2.035l0.776 -6.288 -4.225 -4.699c-1.038 -1.154 -0.422 -3 1.101 -3.3l5.959 -1.173 2.81 -5.418Z" strokeWidth="3" />
												<path id="Ellipse 19" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M24 3a21 21 0 1 0 0 42 21 21 0 1 0 0 -42" strokeWidth="3" />
												<path id="Star 4" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M22.224 12.423c0.747 -1.439 2.805 -1.439 3.551 0l2.81 5.418 5.96 1.174c1.522 0.3 2.138 2.145 1.1 3.3l-4.225 4.698 0.776 6.288c0.195 1.584 -1.45 2.747 -2.878 2.035L24 32.68l-5.318 2.655c-1.427 0.712 -3.073 -0.451 -2.878 -2.035l0.776 -6.288 -4.225 -4.699c-1.038 -1.154 -0.422 -3 1.1 -3.3l5.96 -1.173 2.81 -5.418Z" strokeWidth="3" />
											</g>
										</svg>
										<span className="link-text ml-4">Subscription</span>
									</NavLink>
								</li>

								{/* ------------------------- Get Help  ------------------------- */}
								<li className="mt-2 articles subcategory-menu" ref={helpCenterRef}>
									<NavLink to="" className={({ isActive }) => isActive ? "is-active" : ""}
										onClick={(e) => {
											e.preventDefault();
											checkForUnsavedChanges(e);
											toggleHelpCenter();
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Information-Circle--Streamline-Plump" height="48" width="48">
											<g id="information-circle--information-frame-info-more-help-point-circle"><path id="Ellipse 69" fill="" d="M24 3c11.598 0 21 9.402 21 21s-9.402 21 -21 21S3 35.598 3 24 12.402 3 24 3Z" stroke-width="3"></path>
												<path id="Ellipse 68" fill="" d="M20.5 12.5a3.5 3.5 0 1 0 7 0 3.5 3.5 0 1 0 -7 0" stroke-width="3"></path>
												<path id="Union" fill="" d="M24.944 20.026c1.138 0.066 1.866 0.95 1.937 2.088C26.944 23.14 27 24.697 27 27v5.028c1.27 0.027 2.217 0.068 2.912 0.11 1.094 0.067 1.972 0.74 2.053 1.834 0.022 0.294 0.035 0.634 0.035 1.028s-0.013 0.734 -0.035 1.029c-0.08 1.092 -0.959 1.766 -2.053 1.833 -1.16 0.07 -3.022 0.138 -5.912 0.138s-4.753 -0.068 -5.912 -0.138c-1.094 -0.067 -1.972 -0.74 -2.053 -1.834A14.009 14.009 0 0 1 16 35c0 -0.394 0.013 -0.734 0.035 -1.029 0.08 -1.092 0.959 -1.766 2.053 -1.833a72.898 72.898 0 0 1 2.912 -0.11V27c0 -0.352 0.001 -0.686 0.004 -1.004a32.141 32.141 0 0 1 -1.985 -0.033c-1.187 -0.066 -1.98 -0.941 -2.01 -2.13a33.341 33.341 0 0 1 0 -1.667c0.03 -1.188 0.823 -2.063 2.01 -2.129 0.407 -0.022 0.896 -0.037 1.481 -0.037H24c0.356 0 0.67 0.01 0.944 0.026Z" stroke-width="3"></path>
												<path id="Ellipse 18" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M24 3c11.598 0 21 9.402 21 21s-9.402 21 -21 21S3 35.598 3 24 12.402 3 24 3Z" stroke-width="3"></path>
												<path id="Union_2" fill="" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M24.944 20.026c1.138 0.066 1.866 0.95 1.937 2.088C26.944 23.14 27 24.697 27 27v5.028c1.27 0.027 2.217 0.068 2.912 0.11 1.094 0.067 1.972 0.74 2.053 1.834 0.022 0.294 0.035 0.634 0.035 1.028s-0.013 0.734 -0.035 1.029c-0.08 1.092 -0.959 1.766 -2.053 1.833 -1.16 0.07 -3.022 0.138 -5.912 0.138s-4.753 -0.068 -5.912 -0.138c-1.094 -0.067 -1.972 -0.74 -2.053 -1.834A14.009 14.009 0 0 1 16 35c0 -0.394 0.013 -0.734 0.035 -1.029 0.08 -1.092 0.959 -1.766 2.053 -1.833a72.898 72.898 0 0 1 2.912 -0.11V27c0 -0.352 0.001 -0.686 0.004 -1.004a32.141 32.141 0 0 1 -1.985 -0.033c-1.187 -0.066 -1.98 -0.941 -2.01 -2.13a33.341 33.341 0 0 1 0 -1.667c0.03 -1.188 0.823 -2.063 2.01 -2.129 0.407 -0.022 0.896 -0.037 1.481 -0.037H24c0.356 0 0.67 0.01 0.944 0.026Z" stroke-width="3"></path>
												<path id="Ellipse 67" fill="" stroke="#000000" stroke-linecap="round" stroke-linejoin="round" d="M20.5 12.5a3.5 3.5 0 1 0 7 0 3.5 3.5 0 1 0 -7 0" stroke-width="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4">Help Center</span>
										<span className="drop-down-arrow">
											<svg xmlns="http://www.w3.org/2000/svg" className="arrow-svg" width="24" height="24" viewBox="0 0 24 24" fill="" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"
												style={{
													transform: isHelpCenterDropdownOpen ? 'rotate(90deg)' : 'rotate(0deg)',
												}}>
												<path d="M9 18l6-6-6-6" stroke="black" fill="" />
											</svg>
										</span>
									</NavLink>

									{isHelpCenterDropdownOpen ?
										<ul className="dropdown-menu-container" >

											{/* ------------------------- Knowledgebase ------------------------- */}
											<li className="articles subcategory-menu">
												<NavLink to={"https://abun.com/help"} target="_blank"
													className={({ isActive }) => isActive ? "is-active" : ""}
													onClick={(e) => {
														checkForUnsavedChanges(e);
														setHamburgerActive(false);
													}}>
													<span className="articles-li-text">Knowledgebase</span>
												</NavLink>
											</li>

											{/* ------------------------- Live Chat ------------------------- */}
											<li className="articles subcategory-menu">
												<NavLink to="" id="chatWindowbutton"
													onClick={(e) => {
														e.preventDefault();
														showChatWindow();
													}}>
													<span className="articles-li-text">Live Chat Support</span>
												</NavLink>
											</li>
										</ul>
										: ""}
								</li>

								{/* ------------------------- Other Products  ------------------------- */}
								<li className="mt-2 articles subcategory-menu subcategory-product" ref={dropdownRef}>
									<NavLink to="" className={({ isActive }) => isActive ? "other-product-container is-active" : "other-product-container"}
										onClick={(e) => {
											e.preventDefault();
											checkForUnsavedChanges(e);
											toggleDropdown();
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 48 48" id="Erlenmeyer-Flask--Streamline-Plump" height="48" width="48">
											<g id="erlenmeyer-flask--science-experiment-lab-flask-chemistry-solution">
												<path id="Union" fill="" d="M24 3c-3.965 0 -7.161 0.258 -9.316 0.506 -1.451 0.167 -2.11 1.796 -1.25 2.976l1.957 2.683a4 4 0 0 1 0.737 1.86l0.75 6.004a2.025 2.025 0 0 1 -0.572 1.669C9.814 25.267 6.249 29.973 4.482 32.59c-1.042 1.545 -1.496 3.38 -1.392 5.24l0.013 0.229c0.225 3.698 3.19 6.282 6.886 6.53 3.234 0.217 7.848 0.411 14.01 0.411 6.161 0 10.775 -0.194 14.01 -0.411 3.696 -0.248 6.66 -2.832 6.885 -6.53l0.013 -0.23c0.106 -1.86 -0.349 -3.694 -1.39 -5.238 -1.767 -2.618 -5.332 -7.324 -11.825 -13.893a2.025 2.025 0 0 1 -0.572 -1.669l0.75 -6.003a4 4 0 0 1 0.738 -1.861l1.956 -2.683c0.86 -1.18 0.202 -2.81 -1.249 -2.976C31.161 3.258 27.964 3 24 3Z" strokeWidth="3"></path>
												<path id="Union_2" stroke="#000000" strokeLinejoin="round" d="M24 3c-3.965 0 -7.16 0.258 -9.316 0.506 -1.45 0.167 -2.11 1.796 -1.25 2.976l1.957 2.683a4 4 0 0 1 0.737 1.86l0.75 6.004a2.025 2.025 0 0 1 -0.572 1.669C9.814 25.267 6.25 29.973 4.482 32.59c-1.042 1.544 -1.496 3.379 -1.39 5.239l0.013 0.229c0.225 3.698 3.189 6.282 6.886 6.53 3.234 0.217 7.848 0.411 14.01 0.411 6.16 0 10.774 -0.194 14.009 -0.411 3.696 -0.248 6.66 -2.832 6.885 -6.53l0.014 -0.23c0.105 -1.86 -0.35 -3.694 -1.391 -5.238 -1.767 -2.618 -5.332 -7.324 -11.824 -13.893a2.025 2.025 0 0 1 -0.572 -1.669l0.75 -6.003a4 4 0 0 1 0.737 -1.861l1.957 -2.683c0.86 -1.18 0.201 -2.81 -1.25 -2.976C31.161 3.258 27.965 3 24 3Z" strokeWidth="3"></path>
												<path id="Union_3" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M37 33.762s-1.571 -3.143 -7.333 -8.905" strokeWidth="3"></path>
											</g>
										</svg>
										<span className="link-text ml-4" >Other Products</span>
										<span className="drop-down-arrow">
											<svg xmlns="http://www.w3.org/2000/svg" className="arrow-svg" width="24" height="24" viewBox="0 0 24 24" fill="" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" aria-hidden="true"
												style={{

													transform: isDropdownOpen ? 'rotate(90deg)' : 'rotate(0deg)',
												}}>
												<path d="M9 18l6-6-6-6" stroke="black" fill="" />
											</svg>
										</span>
									</NavLink>

									{isDropdownOpen ?
										<ul className="dropdown-menu-container " >

											<li className="articles subcategory-menu">
												<Link to={"https://deliveryman.ai?ref=abun-dashboard"} target="_blank"
													onClick={() => setIsDropdownOpen(false)}>
													<span className="articles-li-text">Deliveryman.ai</span>
												</Link>
											</li>

											<li className="articles subcategory-menu">
												<Link to={"https://draftss.com?ref=abun-dashboard"} target="_blank"
													onClick={() => setIsDropdownOpen(false)}>
													<span className="articles-li-text">Draftss.com</span>
												</Link>
											</li>

											<li className="articles subcategory-menu">
												<Link to={"https://aicallcenter.co?ref=abun-dashboard"} target="_blank"
													onClick={() => setIsDropdownOpen(false)}>
													<span className="articles-li-text">AICallCenter</span>
												</Link>
											</li>

											<li className="articles subcategory-menu">
												<Link to={"https://clientportalos.com?ref=abun-dashboard"} target="_blank"
													onClick={() => setIsDropdownOpen(false)}>
													<span className="articles-li-text">ClientPortalOS</span>
												</Link>
											</li>

										</ul>
										: ""}

								</li>

								{/* ------------------------- Affiliate Program  ------------------------- */}
								<li className="mt-2 subcategory-menu pricing-plan">
									<NavLink to={"https://abun.com/affiliate"} target="_blank"
										onClick={(e) => {
											checkForUnsavedChanges(e);
											setHamburgerActive(false);
										}}>
										<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="-0.5 -0.5 24 24" height="24" width="24">
											<g>
												<path id="Ellipse 23" fill="" d="M11.5 1.4375c5.557375 0 10.0625 4.505125 10.0625 10.0625s-4.505125 10.0625 -10.0625 10.0625S1.4375 17.057375 1.4375 11.5 5.942625 1.4375 11.5 1.4375Z" strokeWidth="1" />
												<path id="Ellipse 22" fill="" d="M11.5 3.8333333333333335c4.234395833333333 0 7.666666666666667 3.4322708333333334 7.666666666666667 7.666666666666667s-3.4322708333333334 7.666666666666667 -7.666666666666667 7.666666666666667S3.8333333333333335 15.734395833333336 3.8333333333333335 11.5 7.265604166666667 3.8333333333333335 11.5 3.8333333333333335Z" strokeWidth="1" />
												<path id="Ellipse 19" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M11.5 1.4375c5.557375 0 10.0625 4.505125 10.0625 10.0625s-4.505125 10.0625 -10.0625 10.0625S1.4375 17.057375 1.4375 11.5 5.942625 1.4375 11.5 1.4375Z" strokeWidth="1.5" />
												{/* <path id="Ellipse 21" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M11.5 3.8333333333333335c4.234395833333333 0 7.666666666666667 3.4322708333333334 7.666666666666667 7.666666666666667s-3.4322708333333334 7.666666666666667 -7.666666666666667 7.666666666666667S3.8333333333333335 15.734395833333336 3.8333333333333335 11.5 7.265604166666667 3.8333333333333335 11.5 3.8333333333333335Z" strokeWidth="1.5"/> */}
												<path id="Vector" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M13.65625 9.079729166666668S12.79375 8.418958333333334 11.5 8.418958333333334c-1.078125 0 -2.15625 0.6602916666666666 -2.15625 1.5405208333333333 0 2.199854166666667 4.3125 0.87975 4.3125 3.0800833333333335 0 0.8802291666666667 -1.078125 1.5400416666666668 -2.15625 1.5400416666666668 -1.2937500000000002 0 -2.15625 -0.6598125 -2.15625 -0.6598125" strokeWidth="1.5" />
												<path id="Vector 837" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M11.5 8.4194375V7.1875" strokeWidth="1.5" />
												<path id="Vector 838" stroke="#000000" strokeLinecap="round" strokeLinejoin="round" d="M11.5 15.8125v-1.2314583333333333" strokeWidth="1.5" />
											</g>
										</svg>
										<span className="link-text ml-4">Affiliate Program </span>
									</NavLink>
								</li>

							</ul>
						</div>

					</div>

					{/* ------------------------- PROFILE PAGE ------------------------- */}
					<div className={`sidebar-footer ${hamburgerActive ? "footer-position" : ""}`}>
						<ul className={"menu-list"}>

							{/* New Profile UI */}
							<div ref={profileDropdownRef}
								className={`dropdown w-100 ${profileDropdownActive ? "is-active" : ""}`}
								onClick={() => { setProfileDropdownActive(!profileDropdownActive) }}>

								<div className="dropdown-trigger w-100">
									<button className="button w-100 is-rounded" aria-haspopup="true" aria-controls="dropdown-menu" style={{ padding: '1.5rem 0.325rem ' }}>
										<span className="is-flex is-align-items-center">
											<span className="is-flex is-justify-content-center is-align-items-center" style={{ width: '28px', border: '1px solid grey', borderRadius: '50%' }}>
												<b>{props.userName?.charAt(0).toUpperCase()}</b>
											</span>
											<span className="link-text ml-3 is-flex is-flex-direction-column" style={{ lineHeight: '1.2' }}>
												{props.userName}
												<span className="has-text-grey-light truncate-charcter" style={{ fontSize: "0.9rem" }}>{props.emailAddress}</span>
											</span>
										</span>
										<svg xmlns="http://www.w3.org/2000/svg" style={{ marginRight: scrollbarVisible ? '1.45rem' : '0.6rem' }} width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
											<path d="m7 15 5 5 5-5"></path><path d="m7 9 5-5 5 5"></path>
										</svg>
									</button>
								</div>
								<div className="dropdown-menu" id="dropdown-menu" role="menu">
									<div className="dropdown-content profile-content">
										<div className={"dropdown-item profile-dropdown-conatiner"}>
											<Link to={pageURL['profile']} className="profile-section" style={{ paddingTop: '1.26rem' }}
												onClick={(e) => {
													setHamburgerActive(false);
												}}>
												<span className="is-flex is-align-items-center">
													<span className="is-flex is-justify-content-center is-align-items-center" style={{ width: '28px', border: '1px solid grey', borderRadius: '50%' }}>
														<b>{props.userName?.charAt(0).toUpperCase()}</b>
													</span>
													<span className="link-text ml-3 is-flex is-flex-direction-column" style={{ lineHeight: '1.2' }}>
														<b>{props.userName}</b>
														<span className="has-text-grey-light truncate-charcter">{props.emailAddress}</span>
													</span>
												</span>
											</Link>
											<hr className="m-0" />
											{props.currentPlanName !== "LTD" && props.currentPlanName !== "Pro Max" && (
												<>
													<Link to={pageURL['manageSubscription']} className="profile-section">
														<svg xmlns="http://www.w3.org/2000/svg" className="ml-1" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
															<path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path>
															<path d="M20 3v4"></path>
															<path d="M22 5h-4"></path>
															<path d="M4 17v2"></path>
															<path d="M5 18H3"></path>
														</svg>
														<span className="ml-3">
															{(props.currentPlanName === "Trial" || props.currentPlanName === "Basic") && "Upgrade to Pro"}
															{props.currentPlanName === "Pro" && "Upgrade to Pro Max"}
														</span>
													</Link>
													<hr className="m-0" />
												</>
											)}
											<Link to={pageURL['manageSubscription']} className="profile-section">
												<svg xmlns="http://www.w3.org/2000/svg" className="ml-1" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
													<rect width="20" height="14" x="2" y="5" rx="2"></rect>
													<line x1="2" x2="22" y1="10" y2="10"></line>
												</svg>
												<span className="ml-3">Usage & Billing</span>
											</Link>
											<hr className="m-0" />
											<Link to={"/logout"} className="profile-section pb-4">
												<svg xmlns="http://www.w3.org/2000/svg" className="ml-1" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" aria-hidden="true">
													<path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
													<polyline points="16 17 21 12 16 7"></polyline>
													<line x1="21" x2="9" y1="12" y2="12"></line>
												</svg>
												<span className="ml-3">Log out</span>
											</Link>
										</div>
									</div>
								</div>
							</div>
						</ul>
					</div>
				</section>
				<div className={"sidebar-progress-bar"}></div>
			</aside >
			{
				showConnectWebsiteModal &&
				<ConnectWebsite setShowConnectWebsiteModal={setShowConnectWebsiteModal}
					failAlertRef={failAlertRef}
					successAlertRef={successAlertRef}
					basePageData={basePageData}
					navigateOrReload="reload" />
			}
		</>
	)
}
